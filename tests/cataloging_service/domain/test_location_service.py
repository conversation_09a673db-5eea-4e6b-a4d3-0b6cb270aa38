from unittest.mock import Mock

from cataloging_service.constants import error_codes
from cataloging_service.domain.location_service import LocationService
from cataloging_service.models import Country, State, City, Cluster, Locality, MicroMarket, Region
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestLocationService(CatalogingServiceTestCase):
    MOCK_COUNTRY_NAME = 'India'
    MOCK_COUNTRY_ID = 1
    INVALID_COUNTRY_ID = 2

    MOCK_STATE_ID = 1
    MOCK_STATE_NAME = 'Karnataka'
    INVALID_STATE_ID = 2

    MOCK_CITY_ID = 1
    MOCK_CITY_NAME = 'Bangalore'
    INVALID_CITY_ID = 2

    MOCK_CLUSTER_ID = 1
    MOCK_CLUSTER_NAME = 'Bangalore'
    INVALID_CLUSTER_ID = 2

    MOCK_LOCALITY_ID = 1
    MOCK_LOCALITY_NAME = 'HSR'
    INVALID_LOCALITY_ID = 2

    MOCK_REGION_ID = 1
    MOCK_REGION_NAME = 'South'
    INVALID_REGION_ID = 2

    MOCK_MM_ID = 1
    MOCK_MM_NAME = 'HSR'
    INVALID_MM_ID = 2

    def setUp(self):
        self.location_repository = Mock()
        self.messaging_service = Mock()
        self.location_service = LocationService(self.location_repository, self.messaging_service)

    def test_get_all_countries(self):
        mock_countries = self.get_mock_countries()
        self.location_repository.get_all_countries.return_value = mock_countries
        all_countries = self.location_service.get_all_countries()

        self.assertEqual(mock_countries, all_countries)

    def test_get_country_successful(self):
        mock_country = self.get_mock_country()

        self.location_repository.get_country.side_effect = lambda \
                country_id: None if country_id != self.MOCK_COUNTRY_ID else mock_country

        country = self.location_service.get_country(self.MOCK_COUNTRY_ID)

        self.assertEqual(mock_country, country)

    def test_get_country_with_invalid_country_id(self):
        mock_country = self.get_mock_country()

        self.location_repository.get_country.side_effect = lambda \
                country_id: None if country_id != self.MOCK_COUNTRY_ID else mock_country

        try:
            self.location_service.get_country(self.INVALID_COUNTRY_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.COUNTRY_NOT_FOUND)
        else:
            self.fail()

    def test_get_states_of_country(self):
        mock_country = self.get_mock_country()

        self.location_repository.get_country.side_effect = lambda \
                country_id: None if country_id != self.MOCK_COUNTRY_ID else mock_country

        states = self.location_service.get_states_of_country(self.MOCK_COUNTRY_ID)

        self.assertEqual(mock_country.states, states)

    def test_get_states_of_country_with_invalid_country_id(self):
        mock_country = self.get_mock_country()

        self.location_repository.get_country.side_effect = lambda \
                country_id: None if country_id != self.MOCK_COUNTRY_ID else mock_country

        self.location_service.get_states_of_country(self.MOCK_COUNTRY_ID)

        try:
            self.location_service.get_country(self.INVALID_COUNTRY_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.COUNTRY_NOT_FOUND)
        else:
            self.fail()

    def test_get_all_states(self):
        mock_states = self.get_mock_states()
        self.location_repository.get_all_states.return_value = mock_states

        all_states = self.location_service.get_all_states()

        self.assertEqual(mock_states, all_states)

    def test_get_state(self):
        mock_state = self.get_mock_state()

        self.location_repository.get_state.side_effect = lambda \
                state_id: None if state_id != self.MOCK_STATE_ID else mock_state

        state = self.location_service.get_state(self.MOCK_STATE_ID)

        self.assertEqual(mock_state, state)

    def test_get_state_with_invalid_state_id(self):
        mock_state = self.get_mock_state()

        self.location_repository.get_state.side_effect = lambda \
                state_id: None if state_id != self.MOCK_STATE_ID else mock_state

        try:
            self.location_service.get_state(self.INVALID_STATE_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.STATE_NOT_FOUND)
        else:
            self.fail()

    def test_get_cities_of_state(self):
        mock_state = self.get_mock_state()

        self.location_repository.get_state.side_effect = lambda \
                state_id: None if state_id != self.MOCK_STATE_ID else mock_state

        cities = self.location_service.get_cities_of_state(self.MOCK_CITY_ID)

        self.assertEqual(mock_state.cities, cities)

    def test_get_cities_of_state_with_invalid_state_id(self):
        mock_state = self.get_mock_state()

        self.location_repository.get_state.side_effect = lambda \
                state_id: None if state_id != self.MOCK_STATE_ID else mock_state

        try:
            self.location_service.get_cities_of_state(self.INVALID_STATE_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.STATE_NOT_FOUND)
        else:
            self.fail()

    def test_get_clusters_of_state(self):
        mock_state = self.get_mock_state()

        mock_clusters = [city.cluster for city in mock_state.cities]

        self.location_repository.get_state.side_effect = lambda \
                state_id: None if state_id != self.MOCK_STATE_ID else mock_state

        clusters = self.location_service.get_clusters_of_state(self.MOCK_STATE_ID)

        self.assertEqual(mock_clusters, clusters)

    def test_get_all_clusters(self):
        mock_cities = self.get_mock_cities()
        mock_clusters = [city.cluster for city in mock_cities]

        self.location_repository.get_all_cities.return_value = mock_cities
        all_clusters = self.location_service.get_all_clusters()

        self.assertEqual(mock_clusters, all_clusters)

    def test_get_city(self):
        mock_city = self.get_mock_city()

        self.location_repository.get_city.side_effect = lambda \
                city_id: None if city_id != self.MOCK_CITY_ID else mock_city

        city = self.location_service.get_city(self.MOCK_CITY_ID)

        self.assertEqual(mock_city, city)

    def test_get_city_with_invalid_id(self):
        mock_city = self.get_mock_city()

        self.location_repository.get_city.side_effect = lambda \
                city_id: None if city_id != self.MOCK_CITY_ID else mock_city

        try:
            self.location_service.get_city(self.INVALID_CITY_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.CITY_NOT_FOUND)
        else:
            self.fail()

    def test_get_all_cities(self):
        mock_cities = self.get_mock_cities()

        self.location_repository.get_all_cities.return_value = mock_cities

        cities = self.location_service.get_all_cities()

        self.assertEqual(mock_cities, cities)

    def test_get_all_localities(self):
        mock_localities = self.get_mock_localities()

        self.location_repository.get_all_localities.return_value = mock_localities

        localities = self.location_service.get_all_localities()

        self.assertEqual(mock_localities, localities)

    def test_get_localities_of_city(self):
        mock_city = self.get_mock_city()

        self.location_repository.get_city.side_effect = lambda \
                city_id: None if city_id != self.MOCK_CITY_ID else mock_city

        localities = self.location_service.get_localities_of_city(self.MOCK_CITY_ID)

        self.assertEqual(mock_city.localities, localities)

    def test_get_localities_of_city_with_invalid_city_id(self):
        mock_city = self.get_mock_city()

        self.location_repository.get_city.side_effect = lambda \
                city_id: None if city_id != self.MOCK_CITY_ID else mock_city

        try:
            self.location_service.get_localities_of_city(self.INVALID_CITY_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.CITY_NOT_FOUND)
        else:
            self.fail()

    def test_get_all_micromarkets(self):
        mock_micromarkets = self.get_mock_micromarkets()

        self.location_repository.get_all_micro_markets.return_value = mock_micromarkets

        micromarkets = self.location_service.get_all_micro_markets()

        self.assertEqual(mock_micromarkets, micromarkets)

    def test_get_micromarkets_of_city(self):
        mock_city = self.get_mock_city()

        self.location_repository.get_city.side_effect = lambda \
                city_id: None if city_id != self.MOCK_CITY_ID else mock_city

        micromarkets = self.location_service.get_micro_markets_of_city(self.MOCK_CITY_ID)

        self.assertEqual(mock_city.micro_markets, micromarkets)

    def test_get_micromarkets_of_city_with_invalid_city_id(self):
        mock_city = self.get_mock_city()

        self.location_repository.get_city.side_effect = lambda \
                city_id: None if city_id != self.MOCK_CITY_ID else mock_city

        try:
            self.location_service.get_micro_markets_of_city(self.INVALID_CITY_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.CITY_NOT_FOUND)
        else:
            self.fail()

    def test_get_all_regions(self):
        mock_regions = self.get_mock_regions()

        self.location_repository.get_all_regions.return_value = mock_regions

        regions = self.location_service.get_all_regions()

        self.assertEqual(regions, mock_regions)

    def test_get_region(self):
        mock_region = self.get_mock_region()

        self.location_repository.get_region.return_value = mock_region

        region = self.location_service.get_region(self.MOCK_REGION_ID)

        self.assertEqual(region, mock_region)

    def test_get_region_invalid_region(self):
        self.location_repository.get_region.return_value = None

        try:
            self.location_service.get_region(self.INVALID_REGION_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.REGION_NOT_FOUND)
        else:
            self.fail()

    def test_get_clusters_of_region(self):
        mock_region = self.get_mock_region()
        self.location_repository.get_region.return_value = mock_region

        clusters = self.location_service.get_clusters_of_region(self.MOCK_REGION_ID)

        self.assertEqual(mock_region.clusters, clusters)

    def test_get_cluster(self):
        mock_cluster = self.get_mock_cluster()
        self.location_repository.get_cluster.return_value = mock_cluster

        cluster = self.location_service.get_cluster(self.MOCK_CLUSTER_ID)

        self.assertEqual(cluster, mock_cluster)

    def test_get_cluster_invalid_cluster(self):
        self.location_repository.get_cluster.return_value = None

        try:
            self.location_service.get_cluster(self.INVALID_CLUSTER_ID)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.CLUSTER_NOT_FOUND)
        else:
            self.fail()

    def test_get_cities_of_cluster(self):
        mock_cluster = self.get_mock_cluster()
        self.location_repository.get_cluster.return_value = mock_cluster

        cities = self.location_service.get_cities_of_cluster(self.MOCK_CLUSTER_ID)

        self.assertEqual(mock_cluster.cities, cities)

    def test_get_clusters_by_region_and_state_with_valid_values(self):
        mock_state = self.get_mock_state()
        self.location_repository.get_state.return_value = mock_state
        mock_region = self.get_mock_region()
        self.location_repository.get_region.return_value = mock_region
        clusters = self.location_service.get_clusters_by_region_and_state(mock_state.id, mock_region.id)
        self.assertEqual(clusters[0].name, self.MOCK_CLUSTER_NAME)

    def test_get_clusters_by_region_and_state_with_invalid_values(self):
        mock_state = self.get_mock_state()
        self.location_repository.get_state.return_value = mock_state
        clusters = self.location_service.get_clusters_by_region_and_state(mock_state.id, 2)
        self.assertEqual(clusters, [])

    def get_mock_locality(self):
        locality = Locality()
        locality.id = self.MOCK_LOCALITY_ID
        locality.name = self.MOCK_LOCALITY_NAME

        return locality

    def get_mock_localities(self):
        return [self.get_mock_locality()]

    def get_mock_country(self):
        country = Country()
        country.id = self.MOCK_COUNTRY_ID
        country.name = self.MOCK_COUNTRY_NAME
        country.states = self.get_mock_states()

        return country

    def get_mock_state(self):
        state = State()
        state.id = self.MOCK_STATE_ID
        state.name = self.MOCK_STATE_NAME
        state.cities = self.get_mock_cities()

        return state

    def get_mock_city(self):
        city = City()
        city.id = self.MOCK_CITY_ID
        city.name = self.MOCK_CITY_NAME
        city.cluster = self.get_mock_cluster()
        city.localities = self.get_mock_localities()
        city.micro_markets = self.get_mock_micromarkets()

        return city

    def get_mock_cluster(self):
        cluster = Cluster()
        cluster.id = self.MOCK_CLUSTER_ID
        cluster.name = self.MOCK_CLUSTER_NAME
        cluster.region_id = self.MOCK_REGION_ID

        return cluster

    def get_mock_cluster_with_region(self):
        cluster = Cluster()
        cluster.id = self.MOCK_CLUSTER_ID
        cluster.name = self.MOCK_CLUSTER_NAME
        cluster.region_id = self.MOCK_REGION_ID
        cluster.region = self.get_mock_region()

        return cluster

    def get_mock_micromarket(self):
        micromarket = MicroMarket()
        micromarket.id = self.MOCK_MM_ID
        micromarket.name = self.MOCK_MM_NAME

    def get_mock_region(self):
        region = Region()
        region.id = self.MOCK_REGION_ID
        region.name = self.MOCK_REGION_NAME
        region.clusters = self.get_mock_clusters()

        return region

    def get_mock_micromarkets(self):
        return [self.get_mock_micromarket()]

    def get_mock_clusters(self):
        return [self.get_mock_cluster()]

    def get_mock_cities(self):
        return [self.get_mock_city()]

    def get_mock_states(self):
        return [self.get_mock_state()]

    def get_mock_countries(self):
        return [self.get_mock_country()]

    def get_mock_regions(self):
        return [self.get_mock_region()]
