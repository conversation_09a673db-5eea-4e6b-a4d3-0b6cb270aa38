import pytest
from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.area.validation.area_validation import AreaValidation
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.before_test_actions import *


class TestUpdateArea(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id",
        [

            # -----------------Test cases related to area_name--------------------------#
            ("patch_area_01", Patch_Area_01,
             "Update a created area_01", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_02", Patch_Area_02,
             "Update a created area_02", 200, False, None, None, None, <PERSON>LLER_ID[0]),
            ("patch_area_03", Patch_Area_02,
             "Update a created area_04", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_04", Patch_Area_01,
             "Update name of created area", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_05", Patch_Area_01,
             "Update area tables", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_06", Patch_Area_01,
             "Update an area with area having special character", 200, False, None, None, None, SELLER_ID[0]),

            # ---------------------- test cases related to tables --------------------------------------#

            ("patch_area_07", Patch_Area_01,
             "Update an area with multiple tables", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_08", Patch_Area_01,
             "Update an Area having two table", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_09", Patch_Area_01,
             "Update an Area with number of tables as EMPTY", 200, False, None, None, None, SELLER_ID[0]),

            # ----------------------------- test cases related to seat count -----------------------------#

            ("patch_area_10", Patch_Area_01,
             "Update an Area with seat_count having special character", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_11", Patch_Area_01,
             "Update an Area with seat_count as EMPTY", 200, False, None, None, None, SELLER_ID[0]),
            ("patch_area_12", Patch_Area_04,
             "Update an Area with multiple patch", 200, False, None, None, None, SELLER_ID[0]),

        ])
    @pytest.mark.regression
    def test_update_combo(self, client, test_case_id, previous_actions, tc_description, status_code,
                          skip_case, skip_message, error_message, existing_error_message_test_id, seller_id):
        if skip_case:
            pytest.skip(skip_message)

        if previous_actions:
            self.common_request_caller(client, previous_actions, seller_id)

        area_id = self.area_request.area_id
        response = self.area_request.update_area_request(client, test_case_id, seller_id, status_code, area_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(client, test_case_id, response, area_id, seller_id, self.area_request,
                            previous_actions)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, area_id, seller_id, area_request, previous_actions):
        validation = AreaValidation(client, test_case_id, response, area_id, seller_id, area_request, True,
                                     previous_actions)
        validation.validate_update_response(test_case_id)
        validation.validate_get_update_area_response(client, seller_id, area_id, 200, test_case_id)
        validation.validate_area_exist_in_get_areas_response(client, seller_id, 200)
