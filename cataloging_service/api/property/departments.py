from flask import Blueprint
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.department import (
    DepartmentCreateSchema,
    DepartmentResponseSchema,
    PropertyDepartmentQuerySchema,
)
from cataloging_service.domain.entities.properties.property_department import PropertyDepartmentEntity
from cataloging_service.domain.services.property.property_department_service import PropertyDepartmentService
from cataloging_service.common.api_responses import ApiResponse
from cataloging_service.common.request_decorators import parse_body, parse_query
from object_registry import inject

bp = Blueprint("property_departments", __name__)


@bp.route("/<property_id>/departments", methods=["POST"])
@inject(department_service=PropertyDepartmentService)
@parse_body(DepartmentCreateSchema)
@atomic_operation
def create_property_department(property_id: str, data: DepartmentCreateSchema, department_service):
    """
    Create a new property department
    ---
    tags:
      - Property Departments
    summary: Create property department
    description: |
      Creates a new department for a specific property. Departments can be created from
      templates or as custom departments. Supports hierarchical relationships with
      parent departments within the same property.
    parameters:
      - name: property_id
        in: path
        required: true
        schema:
          type: string
        description: Property ID to create department for
        example: "PROP123"
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentCreateSchema'
          example:
            code: "RESTAURANT"
            name: "Restaurant Operations"
            template_code: "RESTAURANT"
            financial_code: "REST"
            parent_id: null
            description: "Property-specific restaurant department"
            is_custom: false
            is_active: true
    responses:
      201:
        description: Property department created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      409:
        description: Conflict - department code already exists for property
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Ensure property_id matches the URL parameter
    department_data = data.model_dump()
    department_data["property_id"] = property_id

    # Create domain entity using automatic conversion
    department_entity = PropertyDepartmentEntity.model_validate(department_data)

    # Use service to create department
    created_department = department_service.create_department(department_entity)

    # Convert to response schema using automatic conversion
    response_data = DepartmentResponseSchema.model_validate(created_department.model_dump())

    return ApiResponse.created(response_data.model_dump())


@bp.route("/<property_id>/departments", methods=["GET"])
@inject(department_service=PropertyDepartmentService)
@parse_query(PropertyDepartmentQuerySchema)
def get_property_departments(property_id: str, filters: PropertyDepartmentQuerySchema, department_service):
    """Get departments for a property"""
    # Get departments using service with filters
    if filters.root_only:
        departments = department_service.get_root_departments(property_id)
    else:
        departments = department_service.get_departments_by_property(property_id, filters.active_only)

    # Convert to response schemas using automatic conversion
    response_departments = [
        DepartmentResponseSchema.model_validate(department.model_dump()).model_dump()
        for department in departments
    ]

    return ApiResponse.success(response_departments)


@bp.route("/<property_id>/departments/<int:department_id>", methods=["GET"])
@inject(department_service=PropertyDepartmentService)
def get_property_department(property_id: str, department_id: int, department_service):
    """
    Get a specific property department
    ---
    tags:
      - Property Departments
    summary: Get property department by ID
    description: |
      Retrieves a specific department for a property by its ID.
      Validates that the department belongs to the specified property.
    parameters:
      - name: property_id
        in: path
        required: true
        schema:
          type: string
        description: Property ID
        example: "PROP123"
      - name: department_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Department ID
        example: 1
    responses:
      200:
        description: Property department details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentResponseSchema'
            example:
              id: 1
              property_id: "PROP123"
              code: "RESTAURANT"
              name: "Restaurant Operations"
              template_code: "RESTAURANT"
              financial_code: "REST"
              parent_id: null
              description: "Property-specific restaurant department"
              is_custom: false
              is_active: true
              created_at: "2024-01-15T10:30:00Z"
              modified_at: "2024-01-15T10:30:00Z"
      404:
        description: Department not found or doesn't belong to property
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    department = department_service.get_department_by_id(department_id)
    
    if not department:
        return ApiResponse.not_found("Department", department_id)
    
    # Verify department belongs to the property
    if department.property_id != property_id:
        return ApiResponse.not_found("Department", department_id)

    response_data = DepartmentResponseSchema.model_validate(department.model_dump())
    return ApiResponse.success(response_data.model_dump())


@bp.route("/<property_id>/departments/<int:department_id>", methods=["PUT"])
@inject(department_service=PropertyDepartmentService)
@parse_body(DepartmentCreateSchema)
@atomic_operation
def replace_property_department(property_id: str, department_id: int, data: DepartmentCreateSchema, department_service):
    """
    Replace a property department (complete resource replacement)
    ---
    tags:
      - Property Departments
    summary: Replace property department
    description: |
      Completely replaces a property department with new data. This is a PUT operation
      that requires all fields to be provided. Validates that the department belongs
      to the specified property and enforces business rules.
    parameters:
      - name: property_id
        in: path
        required: true
        schema:
          type: string
        description: Property ID
        example: "PROP123"
      - name: department_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Department ID to replace
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentCreateSchema'
          example:
            code: "RESTAURANT"
            name: "Updated Restaurant Operations"
            template_code: "RESTAURANT"
            financial_code: "REST"
            parent_id: null
            description: "Updated property-specific restaurant department"
            is_custom: false
            is_active: true
    responses:
      200:
        description: Property department replaced successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Department not found or doesn't belong to property
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - department code already exists for property
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Check if department exists
    existing_department = department_service.get_department_by_id(department_id)
    if not existing_department or existing_department.property_id != property_id:
        return ApiResponse.not_found("Department", department_id)

    # PUT = Complete replacement - use all fields from request
    replacement_data = data.model_dump()
    replacement_data["id"] = department_id
    replacement_data["property_id"] = property_id

    # Create replacement entity (complete replacement)
    replacement_entity = PropertyDepartmentEntity.model_validate(replacement_data)

    # Use service to update department
    updated_department = department_service.update_department(replacement_entity)

    # Convert to response schema using automatic conversion
    response_data = DepartmentResponseSchema.model_validate(updated_department.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/<property_id>/departments/<int:department_id>", methods=["DELETE"])
@inject(department_service=PropertyDepartmentService)
@atomic_operation
def delete_property_department(property_id: str, department_id: int, department_service):
    """
    Delete a property department
    ---
    tags:
      - Property Departments
    summary: Delete property department
    description: |
      Permanently deletes a property department. This action cannot be undone.
      Validates that the department belongs to the specified property.
      Use with caution as it may affect SKUs, sellers, and transactions
      associated with this department.
    parameters:
      - name: property_id
        in: path
        required: true
        schema:
          type: string
        description: Property ID
        example: "PROP123"
      - name: department_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Department ID to delete
        example: 1
    responses:
      200:
        description: Property department deleted successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Department deleted successfully"
                request_id:
                  type: string
                  format: uuid
      404:
        description: Department not found or doesn't belong to property
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    # Check if department exists and belongs to property
    existing_department = department_service.get_department_by_id(department_id)
    if not existing_department or existing_department.property_id != property_id:
        return ApiResponse.not_found("Department", department_id)
    
    success = department_service.delete_department(department_id)
    
    if not success:
        return ApiResponse.not_found("Department", department_id)

    return ApiResponse.success({"message": "Department deleted successfully"})
