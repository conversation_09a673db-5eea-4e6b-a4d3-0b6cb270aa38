from flask import Blueprint
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.department import (
    DepartmentTemplateCreateSchema,
    DepartmentTemplateUpdateSchema,
    DepartmentTemplateResponseSchema,
    DepartmentTemplateQuerySchema,
)
from cataloging_service.domain.entities.templates.department_template import DepartmentTemplateEntity
from cataloging_service.domain.services.template.department_template_service import DepartmentTemplateService
from cataloging_service.common.api_responses import ApiResponse
from cataloging_service.common.request_decorators import parse_body, parse_query
from object_registry import inject

bp = Blueprint("template_departments", __name__)


@bp.route("/departments", methods=["POST"])
@inject(department_service=DepartmentTemplateService)
@parse_body(DepartmentTemplateCreateSchema)
@atomic_operation
def create_department_template(data: DepartmentTemplateCreateSchema, department_service):
    """
    Create a new department template
    ---
    tags:
      - Department Templates
    summary: Create department template
    description: |
      Creates a new brand-level department template. These templates define the structure
      and default values for departments that will be created at the property level.
      Supports hierarchical departments with parent-child relationships.
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentTemplateCreateSchema'
          example:
            brand_id: 1
            code: "RESTAURANT"
            name: "Restaurant Operations"
            financial_code: "REST"
            parent_code: null
            description: "Restaurant and food service operations"
            auto_create_on_property_launch: true
            is_active: true
    responses:
      201:
        description: Department template created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
      400:
        description: Bad request - validation errors
      409:
        description: Conflict - department template code already exists for brand
    """
    # Create domain entity using automatic conversion
    department_entity = DepartmentTemplateEntity.model_validate(data.model_dump())

    # Use service to create department template
    created_department = department_service.create_department_template(department_entity)

    # Convert to response schema using automatic conversion
    response_data = DepartmentTemplateResponseSchema.model_validate(created_department.model_dump())

    return ApiResponse.created(response_data.model_dump())


@bp.route("/departments", methods=["GET"])
@inject(department_service=DepartmentTemplateService)
@parse_query(DepartmentTemplateQuerySchema)
def get_department_templates(filters: DepartmentTemplateQuerySchema, department_service):
    """
    Get department templates with optional filters
    ---
    tags:
      - Department Templates
    summary: List department templates
    description: |
      Retrieves department templates for a brand with optional filtering.
      Returns all templates that can be used to create departments at properties.
    parameters:
      - name: brand_id
        in: query
        required: true
        schema:
          type: integer
          minimum: 1
        description: Brand ID to filter templates
        example: 1
      - name: active_only
        in: query
        required: false
        schema:
          type: boolean
          default: true
        description: Filter only active templates
        example: true
    responses:
      200:
        description: List of department templates
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
            example:
              - id: 1
                brand_id: 1
                code: "RESTAURANT"
                name: "Restaurant Operations"
                financial_code: "REST"
                parent_code: null
                description: "Restaurant and food service operations"
                auto_create_on_property_launch: true
                is_active: true
                created_at: "2024-01-15T10:30:00Z"
                modified_at: "2024-01-15T10:30:00Z"
      400:
        description: Bad request - missing or invalid parameters
    """
    # Get department templates using service with filters
    department_templates = department_service.get_department_templates_by_brand(filters.brand_id, filters.active_only)

    # Convert to response schemas using automatic conversion
    response_templates = [
        DepartmentTemplateResponseSchema.model_validate(template.model_dump()).model_dump()
        for template in department_templates
    ]

    return ApiResponse.success(response_templates)


@bp.route("/departments/<int:template_id>", methods=["GET"])
@inject(department_service=DepartmentTemplateService)
def get_department_template(template_id: int, department_service):
    """
    Get a specific department template
    ---
    tags:
      - Department Templates
    summary: Get department template by ID
    description: |
      Retrieves a specific department template by its ID. Use this to get detailed
      information about a template including its hierarchy relationships and configuration.
    parameters:
      - name: template_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Department template ID
        example: 1
    responses:
      200:
        description: Department template details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
            example:
              id: 1
              brand_id: 1
              code: "RESTAURANT"
              name: "Restaurant Operations"
              financial_code: "REST"
              parent_code: null
              description: "Restaurant and food service operations"
              auto_create_on_property_launch: true
              is_active: true
              created_at: "2024-01-15T10:30:00Z"
              modified_at: "2024-01-15T10:30:00Z"
      404:
        description: Department template not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    department_template = department_service.get_department_template_by_id(template_id)

    if not department_template:
        return ApiResponse.not_found("Department Template", template_id)

    response_data = DepartmentTemplateResponseSchema.model_validate(department_template.model_dump())
    return ApiResponse.success(response_data.model_dump())


@bp.route("/departments/<int:template_id>", methods=["PUT"])
@inject(department_service=DepartmentTemplateService)
@parse_body(DepartmentTemplateCreateSchema)
@atomic_operation
def replace_department_template(data: DepartmentTemplateCreateSchema, template_id: int, department_service):
    """
    Replace a department template (complete resource replacement)
    ---
    tags:
      - Department Templates
    summary: Replace department template
    description: |
      Completely replaces a department template with new data. This is a PUT operation
      that requires all fields to be provided. Use PATCH for partial updates.
      Validates hierarchy relationships and business rules.
    parameters:
      - name: template_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Department template ID to replace
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentTemplateCreateSchema'
          example:
            brand_id: 1
            code: "RESTAURANT"
            name: "Restaurant Operations Updated"
            financial_code: "REST"
            parent_code: null
            description: "Updated restaurant and food service operations"
            auto_create_on_property_launch: true
            is_active: true
    responses:
      200:
        description: Department template replaced successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Department template not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - code already exists or hierarchy violation
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Check if template exists
    existing_department = department_service.get_department_template_by_id(template_id)
    if not existing_department:
        return ApiResponse.not_found("Department Template", template_id)

    # PUT = Complete replacement - use all fields from request
    replacement_data = data.model_dump()
    replacement_data["id"] = template_id

    # Create replacement entity (complete replacement)
    replacement_entity = DepartmentTemplateEntity.model_validate(replacement_data)

    # Use service to update department template
    updated_department = department_service.update_department_template(replacement_entity)

    # Convert to response schema using automatic conversion
    response_data = DepartmentTemplateResponseSchema.model_validate(updated_department.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/departments/<int:template_id>", methods=["PATCH"])
@inject(department_service=DepartmentTemplateService)
@parse_body(DepartmentTemplateUpdateSchema)
@atomic_operation
def patch_department_template(data: DepartmentTemplateUpdateSchema, template_id: int, department_service):
    """
    Partially update a department template (only provided fields)
    ---
    tags:
      - Department Templates
    summary: Update department template
    description: |
      Partially updates a department template with only the provided fields.
      This is a PATCH operation that merges new values with existing data.
      Validates hierarchy relationships and business rules for updated fields.
    parameters:
      - name: template_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Department template ID to update
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentTemplateUpdateSchema'
          example:
            name: "Updated Restaurant Operations"
            description: "Updated description for restaurant operations"
            auto_create_on_property_launch: false
    responses:
      200:
        description: Department template updated successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Department template not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - code already exists or hierarchy violation
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Get existing department template
    existing_department = department_service.get_department_template_by_id(template_id)
    if not existing_department:
        return ApiResponse.not_found("Department Template", template_id)

    # PATCH = Partial update - merge only provided fields
    update_data = data.model_dump(exclude_unset=True)
    update_data["id"] = template_id

    # Create updated entity by merging existing with provided fields
    updated_entity = DepartmentTemplateEntity.model_validate(
        {**existing_department.model_dump(), **update_data}
    )

    # Use service to update department template
    updated_department = department_service.update_department_template(updated_entity)

    # Convert to response schema using automatic conversion
    response_data = DepartmentTemplateResponseSchema.model_validate(updated_department.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/departments/<int:template_id>", methods=["DELETE"])
@inject(department_service=DepartmentTemplateService)
@atomic_operation
def delete_department_template(template_id: int, department_service):
    """
    Delete a department template
    ---
    tags:
      - Department Templates
    summary: Delete department template
    description: |
      Permanently deletes a department template. This action cannot be undone.
      Use with caution as it may affect profit center templates that reference
      this department template and properties that use this template.
    parameters:
      - name: template_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Department template ID to delete
        example: 1
    responses:
      200:
        description: Department template deleted successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Department template deleted successfully"
                request_id:
                  type: string
                  format: uuid
      404:
        description: Department template not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    success = department_service.delete_department_template(template_id)
    
    if not success:
        return ApiResponse.not_found("Department Template", template_id)

    return ApiResponse.success({"message": "Department template deleted successfully"})
