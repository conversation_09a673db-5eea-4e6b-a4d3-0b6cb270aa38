from flask import Blueprint
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.department import (
    ProfitCenterTemplateCreateSchema,
    ProfitCenterTemplateResponseSchema,
    ProfitCenterTemplateQuerySchema,
)
from cataloging_service.domain.entities.templates.profit_center_template import ProfitCenterTemplateEntity
from cataloging_service.domain.services.template.profit_center_template_service import ProfitCenterTemplateService
from cataloging_service.common.api_responses import ApiResponse, api_endpoint
from cataloging_service.common.request_decorators import parse_body, parse_query
from object_registry import inject

bp = Blueprint("template_profit_centers", __name__)


@bp.route("/profit-centers", methods=["POST"])
@inject(profit_center_service=ProfitCenterTemplateService)
@parse_body(ProfitCenterTemplateCreateSchema)
@api_endpoint
@atomic_operation
def create_profit_center_template(data: ProfitCenterTemplateCreateSchema, profit_center_service):
    """
    Create a new profit center template
    ---
    tags:
      - Profit Center Templates
    summary: Create profit center template
    description: |
      Creates a new brand-level profit center template. Profit centers are associated
      with department templates and define specific operational units within departments
      (e.g., "DINING" within "RESTAURANT" department).
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ProfitCenterTemplateCreateSchema'
          example:
            brand_id: 1
            code: "DINING"
            name: "Dining Operations"
            department_template_code: "RESTAURANT"
            system_interface: "POS_SYSTEM"
            description: "Restaurant dining area operations"
            auto_create_on_property_launch: true
            is_active: true
    responses:
      201:
        description: Profit center template created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProfitCenterTemplateResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      409:
        description: Conflict - profit center code already exists for brand
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Create domain entity using automatic conversion
    profit_center_entity = ProfitCenterTemplateEntity.model_validate(data.model_dump())

    # Use service to create profit center template
    created_profit_center = profit_center_service.create_profit_center_template(profit_center_entity)

    # Convert to response schema using automatic conversion
    response_data = ProfitCenterTemplateResponseSchema.model_validate(created_profit_center.model_dump())

    return ApiResponse.created(response_data.model_dump())


@bp.route("/profit-centers", methods=["GET"])
@inject(profit_center_service=ProfitCenterTemplateService)
@parse_query(ProfitCenterTemplateQuerySchema)
@api_endpoint
def get_profit_center_templates(filters: ProfitCenterTemplateQuerySchema, profit_center_service):
    """Get profit center templates with optional filters"""
    # Get profit center templates using service with filters
    if filters.department_template_code:
        profit_center_templates = profit_center_service.get_by_department_template(filters.brand_id, filters.department_template_code)
    else:
        profit_center_templates = profit_center_service.get_profit_center_templates_by_brand(filters.brand_id, filters.active_only)

    # Convert to response schemas using automatic conversion
    response_templates = [
        ProfitCenterTemplateResponseSchema.model_validate(template.model_dump()).model_dump()
        for template in profit_center_templates
    ]

    return ApiResponse.success(response_templates)


@bp.route("/profit-centers/<int:template_id>", methods=["GET"])
@inject(profit_center_service=ProfitCenterTemplateService)
@api_endpoint
def get_profit_center_template(template_id: int, profit_center_service):
    """
    Get a specific profit center template
    ---
    tags:
      - Profit Center Templates
    summary: Get profit center template by ID
    description: |
      Retrieves a specific profit center template by its ID. Use this to get detailed
      information about a profit center template including its department association.
    parameters:
      - name: template_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Profit center template ID
        example: 1
    responses:
      200:
        description: Profit center template details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProfitCenterTemplateResponseSchema'
            example:
              id: 1
              brand_id: 1
              code: "DINING"
              name: "Dining Operations"
              department_template_code: "RESTAURANT"
              system_interface: "POS_SYSTEM"
              description: "Restaurant dining area operations"
              auto_create_on_property_launch: true
              is_active: true
              created_at: "2024-01-15T10:30:00Z"
              modified_at: "2024-01-15T10:30:00Z"
      404:
        description: Profit center template not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    profit_center_template = profit_center_service.get_profit_center_template_by_id(template_id)

    if not profit_center_template:
        return ApiResponse.not_found("Profit Center Template", template_id)

    response_data = ProfitCenterTemplateResponseSchema.model_validate(profit_center_template.model_dump())
    return ApiResponse.success(response_data.model_dump())


@bp.route("/profit-centers/<int:template_id>", methods=["PUT"])
@inject(profit_center_service=ProfitCenterTemplateService)
@parse_body(ProfitCenterTemplateCreateSchema)
@api_endpoint
@atomic_operation
def replace_profit_center_template(data: ProfitCenterTemplateCreateSchema, template_id: int, profit_center_service):
    """
    Replace a profit center template (complete resource replacement)
    ---
    tags:
      - Profit Center Templates
    summary: Replace profit center template
    description: |
      Completely replaces a profit center template with new data. This is a PUT operation
      that requires all fields to be provided. Use PATCH for partial updates.
      Validates business rules including department template association.
    parameters:
      - name: template_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Profit center template ID to replace
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ProfitCenterTemplateCreateSchema'
          example:
            brand_id: 1
            code: "DINING"
            name: "Updated Dining Operations"
            department_template_code: "RESTAURANT"
            system_interface: "UPDATED_POS_SYSTEM"
            description: "Updated restaurant dining area operations"
            auto_create_on_property_launch: true
            is_active: true
    responses:
      200:
        description: Profit center template replaced successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProfitCenterTemplateResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Profit center template not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - code already exists for brand
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Check if template exists
    existing_profit_center = profit_center_service.get_profit_center_template_by_id(template_id)
    if not existing_profit_center:
        return ApiResponse.not_found("Profit Center Template", template_id)

    # PUT = Complete replacement - use all fields from request
    replacement_data = data.model_dump()
    replacement_data["id"] = template_id

    # Create replacement entity (complete replacement)
    replacement_entity = ProfitCenterTemplateEntity.model_validate(replacement_data)

    # Use service to update profit center template
    updated_profit_center = profit_center_service.update_profit_center_template(replacement_entity)

    # Convert to response schema using automatic conversion
    response_data = ProfitCenterTemplateResponseSchema.model_validate(updated_profit_center.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/profit-centers/<int:template_id>", methods=["DELETE"])
@inject(profit_center_service=ProfitCenterTemplateService)
@api_endpoint
@atomic_operation
def delete_profit_center_template(template_id: int, profit_center_service):
    """
    Delete a profit center template
    ---
    tags:
      - Profit Center Templates
    summary: Delete profit center template
    description: |
      Permanently deletes a profit center template. This action cannot be undone.
      Use with caution as it may affect properties that use this template for
      profit center creation.
    parameters:
      - name: template_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Profit center template ID to delete
        example: 1
    responses:
      200:
        description: Profit center template deleted successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Profit center template deleted successfully"
                request_id:
                  type: string
                  format: uuid
      404:
        description: Profit center template not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    success = profit_center_service.delete_profit_center_template(template_id)
    
    if not success:
        return ApiResponse.not_found("Profit Center Template", template_id)

    return ApiResponse.success({"message": "Profit center template deleted successfully"})
