from flask import Blueprint
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.transaction import (
    TransactionDefaultMappingCreateSchema,
    TransactionDefaultMappingUpdateSchema,
    TransactionDefaultMappingResponseSchema,
    BulkTransactionDefaultMappingCreateSchema,
    TransactionDefaultMappingQuerySchema,
    TransactionMappingLookupQuerySchema,
    TransactionTypesQuerySchema,
)
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.domain.services.transactions.transaction_default_mapping_service import TransactionDefaultMappingService
from cataloging_service.common.api_responses import ApiResponse
from cataloging_service.common.request_decorators import parse_body, parse_query
from object_registry import inject

bp = Blueprint("transaction_default_mappings", __name__)


@bp.route("/default-mappings", methods=["POST"])
@inject(mapping_service=TransactionDefaultMappingService)
@parse_body(TransactionDefaultMappingCreateSchema)
@atomic_operation
def create_default_mapping(data: TransactionDefaultMappingCreateSchema, mapping_service):
    """
    Create a new transaction default mapping
    ---
    tags:
      - Transaction Default Mappings
    summary: Create transaction default mapping
    description: |
      Creates a new transaction default mapping for a brand. These mappings define default values
      (GL codes, ERP IDs, particulars) that are automatically applied when creating transactions
      of specific types within departments.
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TransactionDefaultMappingCreateSchema'
          example:
            brand_id: 1
            transaction_type: "SALE"
            transaction_type_code: "ROOM_SALE"
            entity_type: "BOOKING"
            default_gl_code: "4100"
            default_erp_id: "ROOM_REV"
            default_particulars: "Room Revenue"
            default_is_merge: false
            transaction_details:
              category: "revenue"
              department_applicable: true
            is_active: true
    responses:
      201:
        description: Transaction default mapping created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionDefaultMappingResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Validation Failed"
                message:
                  type: string
                  example: "Request validation failed with 2 error(s)"
                validation_errors:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      message:
                        type: string
                      invalid_value:
                        type: string
      409:
        description: Conflict - mapping already exists
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Conflict"
                message:
                  type: string
                  example: "Transaction default mapping already exists"
    """
    # Create domain entity using automatic conversion
    mapping_entity = TransactionDefaultMappingEntity.model_validate(data.model_dump())

    # Use service to create mapping
    created_mapping = mapping_service.create_default_mapping(mapping_entity)

    # Convert to response schema using automatic conversion
    response_data = TransactionDefaultMappingResponseSchema.model_validate(created_mapping.model_dump())

    return ApiResponse.created(response_data.model_dump())


@bp.route("/default-mappings/bulk", methods=["POST"])
@inject(mapping_service=TransactionDefaultMappingService)
@parse_body(BulkTransactionDefaultMappingCreateSchema)
@atomic_operation
def bulk_create_default_mappings(data: BulkTransactionDefaultMappingCreateSchema, mapping_service):
    """
    Bulk create transaction default mappings
    ---
    tags:
      - Transaction Default Mappings
    summary: Bulk create transaction default mappings
    description: |
      Creates multiple transaction default mappings in a single operation. Useful for setting up
      default mappings for a new brand or importing configurations. Failed mappings are skipped
      and reported in the response.
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BulkTransactionDefaultMappingCreateSchema'
          example:
            mappings:
              - brand_id: 1
                transaction_type: "SALE"
                transaction_type_code: "ROOM_SALE"
                entity_type: "BOOKING"
                default_gl_code: "4100"
                default_erp_id: "ROOM_REV"
                default_particulars: "Room Revenue"
              - brand_id: 1
                transaction_type: "SALE"
                transaction_type_code: "FB_SALE"
                entity_type: "ORDER"
                default_gl_code: "4200"
                default_erp_id: "FB_REV"
                default_particulars: "Food & Beverage Revenue"
    responses:
      201:
        description: Bulk creation completed (may include partial failures)
        content:
          application/json:
            schema:
              type: object
              properties:
                created_count:
                  type: integer
                  description: Number of successfully created mappings
                  example: 2
                mappings:
                  type: array
                  items:
                    $ref: '#/components/schemas/TransactionDefaultMappingResponseSchema'
      400:
        description: Bad request - validation errors
    """
    # Convert to domain entities
    mapping_entities = [
        TransactionDefaultMappingEntity.model_validate(mapping.model_dump())
        for mapping in data.mappings
    ]

    # Use service to bulk create mappings
    created_mappings = mapping_service.bulk_create_default_mappings(mapping_entities)

    # Convert to response schemas
    response_mappings = [
        TransactionDefaultMappingResponseSchema.model_validate(mapping.model_dump()).model_dump()
        for mapping in created_mappings
    ]

    return ApiResponse.created({
        "created_count": len(created_mappings),
        "mappings": response_mappings
    })


@bp.route("/default-mappings", methods=["GET"])
@inject(mapping_service=TransactionDefaultMappingService)
@parse_query(TransactionDefaultMappingQuerySchema)
def get_default_mappings(filters: TransactionDefaultMappingQuerySchema, mapping_service):
    """
    Get transaction default mappings with optional filters
    ---
    tags:
      - Transaction Default Mappings
    summary: List transaction default mappings
    description: |
      Retrieves transaction default mappings for a brand with optional filtering by transaction type
      and entity type. Use this to get all configured default mappings or find specific ones.
    parameters:
      - name: brand_id
        in: query
        required: true
        schema:
          type: integer
          minimum: 1
        description: Brand ID to filter mappings
        example: 1
      - name: transaction_type
        in: query
        required: false
        schema:
          type: string
        description: Filter by transaction type (e.g., SALE, REFUND)
        example: "SALE"
      - name: entity_type
        in: query
        required: false
        schema:
          type: string
        description: Filter by entity type (e.g., BOOKING, ORDER)
        example: "BOOKING"
      - name: active_only
        in: query
        required: false
        schema:
          type: boolean
          default: true
        description: Filter only active mappings
        example: true
    responses:
      200:
        description: List of transaction default mappings
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/TransactionDefaultMappingResponseSchema'
            example:
              - id: 1
                brand_id: 1
                transaction_type: "SALE"
                transaction_type_code: "ROOM_SALE"
                entity_type: "BOOKING"
                default_gl_code: "4100"
                default_erp_id: "ROOM_REV"
                default_particulars: "Room Revenue"
                default_is_merge: false
                transaction_details:
                  category: "revenue"
                is_active: true
                created_at: "2024-01-15T10:30:00Z"
                modified_at: "2024-01-15T10:30:00Z"
      400:
        description: Bad request - missing or invalid parameters
    """
    # Get mappings using service with filters
    if filters.transaction_type and filters.entity_type:
        mappings = mapping_service.get_mappings_by_type(filters.brand_id, filters.transaction_type, filters.entity_type)
    else:
        mappings = mapping_service.get_default_mappings_by_brand(filters.brand_id, filters.active_only)

    # Convert to response schemas
    response_mappings = [
        TransactionDefaultMappingResponseSchema.model_validate(mapping.model_dump()).model_dump()
        for mapping in mappings
    ]

    return ApiResponse.success(response_mappings)


@bp.route("/default-mappings/<int:mapping_id>", methods=["GET"])
@inject(mapping_service=TransactionDefaultMappingService)
def get_default_mapping(mapping_id: int, mapping_service):
    """
    Get a specific transaction default mapping
    ---
    tags:
      - Transaction Default Mappings
    summary: Get transaction default mapping by ID
    description: Retrieves a specific transaction default mapping by its ID
    parameters:
      - name: mapping_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction default mapping ID
        example: 1
    responses:
      200:
        description: Transaction default mapping details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionDefaultMappingResponseSchema'
      404:
        description: Transaction default mapping not found
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Not Found"
                message:
                  type: string
                  example: "Transaction Default Mapping not found"
    """
    mapping = mapping_service.get_default_mapping_by_id(mapping_id)

    if not mapping:
        return ApiResponse.not_found("Transaction Default Mapping", mapping_id)

    response_data = TransactionDefaultMappingResponseSchema.model_validate(mapping.model_dump())
    return ApiResponse.success(response_data.model_dump())


@bp.route("/default-mappings/<int:mapping_id>", methods=["PUT"])
@inject(mapping_service=TransactionDefaultMappingService)
@parse_body(TransactionDefaultMappingCreateSchema)
@atomic_operation
def replace_default_mapping(data: TransactionDefaultMappingCreateSchema, mapping_id: int, mapping_service):
    """
    Replace a transaction default mapping (complete resource replacement)
    ---
    tags:
      - Transaction Default Mappings
    summary: Replace transaction default mapping
    description: |
      Completely replaces a transaction default mapping with new data. This is a PUT operation
      that requires all fields to be provided. Use PATCH for partial updates.
      Validates business rules including uniqueness constraints.
    parameters:
      - name: mapping_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction default mapping ID to replace
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TransactionDefaultMappingCreateSchema'
          example:
            brand_id: 1
            transaction_type: "SALE"
            transaction_type_code: "ROOM_SALE"
            entity_type: "BOOKING"
            default_gl_code: "4100"
            default_erp_id: "ROOM_REV"
            default_particulars: "Updated Room Revenue"
            default_is_merge: false
            transaction_details:
              category: "revenue"
              updated: true
            is_active: true
    responses:
      200:
        description: Transaction default mapping replaced successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionDefaultMappingResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Transaction default mapping not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - mapping criteria already exists
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Check if mapping exists
    existing_mapping = mapping_service.get_default_mapping_by_id(mapping_id)
    if not existing_mapping:
        return ApiResponse.not_found("Transaction Default Mapping", mapping_id)

    # PUT = Complete replacement - use all fields from request
    replacement_data = data.model_dump()
    replacement_data["id"] = mapping_id

    # Create replacement entity
    replacement_entity = TransactionDefaultMappingEntity.model_validate(replacement_data)

    # Use service to update mapping
    updated_mapping = mapping_service.update_default_mapping(replacement_entity)

    # Convert to response schema
    response_data = TransactionDefaultMappingResponseSchema.model_validate(updated_mapping.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/default-mappings/<int:mapping_id>", methods=["PATCH"])
@inject(mapping_service=TransactionDefaultMappingService)
@parse_body(TransactionDefaultMappingUpdateSchema)
@atomic_operation
def patch_default_mapping(data: TransactionDefaultMappingUpdateSchema, mapping_id: int, mapping_service):
    """
    Partially update a transaction default mapping
    ---
    tags:
      - Transaction Default Mappings
    summary: Update transaction default mapping
    description: |
      Partially updates a transaction default mapping with only the provided fields.
      This is a PATCH operation that merges new values with existing data.
      Only provided fields will be updated, others remain unchanged.
    parameters:
      - name: mapping_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction default mapping ID to update
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TransactionDefaultMappingUpdateSchema'
          example:
            default_gl_code: "4150"
            default_particulars: "Updated Room Revenue Particulars"
            transaction_details:
              updated_field: "new_value"
    responses:
      200:
        description: Transaction default mapping updated successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionDefaultMappingResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Transaction default mapping not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - updated criteria already exists
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Get existing mapping
    existing_mapping = mapping_service.get_default_mapping_by_id(mapping_id)
    if not existing_mapping:
        return ApiResponse.not_found("Transaction Default Mapping", mapping_id)

    # PATCH = Partial update - merge only provided fields
    update_data = data.model_dump(exclude_unset=True)
    update_data["id"] = mapping_id

    # Create updated entity by merging existing with provided fields
    updated_entity = TransactionDefaultMappingEntity.model_validate(
        {**existing_mapping.model_dump(), **update_data}
    )

    # Use service to update mapping
    updated_mapping = mapping_service.update_default_mapping(updated_entity)

    # Convert to response schema
    response_data = TransactionDefaultMappingResponseSchema.model_validate(updated_mapping.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/default-mappings/<int:mapping_id>", methods=["DELETE"])
@inject(mapping_service=TransactionDefaultMappingService)
@atomic_operation
def delete_default_mapping(mapping_id: int, mapping_service):
    """
    Delete a transaction default mapping
    ---
    tags:
      - Transaction Default Mappings
    summary: Delete transaction default mapping
    description: |
      Permanently deletes a transaction default mapping. This action cannot be undone.
      Use with caution as it may affect future transaction creation that relies on this mapping.
    parameters:
      - name: mapping_id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction default mapping ID to delete
        example: 1
    responses:
      200:
        description: Transaction default mapping deleted successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Transaction default mapping deleted successfully"
                request_id:
                  type: string
                  format: uuid
      404:
        description: Transaction default mapping not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    success = mapping_service.delete_default_mapping(mapping_id)
    
    if not success:
        return ApiResponse.not_found("Transaction Default Mapping", mapping_id)

    return ApiResponse.success({"message": "Transaction default mapping deleted successfully"})


@bp.route("/default-mappings/lookup", methods=["GET"])
@inject(mapping_service=TransactionDefaultMappingService)
@parse_query(TransactionMappingLookupQuerySchema)
def lookup_default_mapping(filters: TransactionMappingLookupQuerySchema, mapping_service):
    """
    Lookup a specific default mapping by transaction criteria
    ---
    tags:
      - Transaction Default Mappings
    summary: Lookup transaction default mapping
    description: |
      Finds a specific transaction default mapping by exact criteria match.
      Use this to get the default mapping that would be applied for a specific
      transaction type, code, and entity type combination.
    parameters:
      - name: brand_id
        in: query
        required: true
        schema:
          type: integer
          minimum: 1
        description: Brand ID
        example: 1
      - name: transaction_type
        in: query
        required: true
        schema:
          type: string
        description: Transaction type (e.g., SALE, REFUND)
        example: "SALE"
      - name: transaction_type_code
        in: query
        required: true
        schema:
          type: string
        description: Specific transaction type code (e.g., ROOM_SALE)
        example: "ROOM_SALE"
      - name: entity_type
        in: query
        required: true
        schema:
          type: string
        description: Entity type (e.g., BOOKING, ORDER)
        example: "BOOKING"
    responses:
      200:
        description: Transaction default mapping found
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionDefaultMappingResponseSchema'
            example:
              id: 1
              brand_id: 1
              transaction_type: "SALE"
              transaction_type_code: "ROOM_SALE"
              entity_type: "BOOKING"
              default_gl_code: "4100"
              default_erp_id: "ROOM_REV"
              default_particulars: "Room Revenue"
              is_active: true
      404:
        description: No matching transaction default mapping found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      400:
        description: Bad request - missing required parameters
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
    """
    mapping = mapping_service.get_mapping_for_transaction(
        filters.brand_id, filters.transaction_type, filters.transaction_type_code, filters.entity_type
    )

    if not mapping:
        return ApiResponse.not_found("Transaction Default Mapping", f"{filters.transaction_type}-{filters.transaction_type_code}-{filters.entity_type}")

    response_data = TransactionDefaultMappingResponseSchema.model_validate(mapping.model_dump())
    return ApiResponse.success(response_data.model_dump())


@bp.route("/default-mappings/types", methods=["GET"])
@inject(mapping_service=TransactionDefaultMappingService)
@parse_query(TransactionTypesQuerySchema)
def get_transaction_types(filters: TransactionTypesQuerySchema, mapping_service):
    """
    Get available transaction types for a brand
    ---
    tags:
      - Transaction Default Mappings
    summary: Get available transaction and entity types
    description: |
      Retrieves all unique transaction types and entity types that have been
      configured for a specific brand. Use this to discover what mapping
      configurations are available for transaction creation.
    parameters:
      - name: brand_id
        in: query
        required: true
        schema:
          type: integer
          minimum: 1
        description: Brand ID to get types for
        example: 1
    responses:
      200:
        description: Available transaction and entity types
        content:
          application/json:
            schema:
              type: object
              properties:
                transaction_types:
                  type: array
                  items:
                    type: string
                  description: List of available transaction types
                  example: ["SALE", "REFUND", "PAYMENT", "ADJUSTMENT"]
                entity_types:
                  type: array
                  items:
                    type: string
                  description: List of available entity types
                  example: ["BOOKING", "ORDER", "PAYMENT", "INVOICE"]
            example:
              transaction_types: ["SALE", "REFUND", "PAYMENT"]
              entity_types: ["BOOKING", "ORDER", "PAYMENT"]
      400:
        description: Bad request - missing brand_id parameter
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
    """
    transaction_types = mapping_service.get_available_transaction_types(filters.brand_id)
    entity_types = mapping_service.get_available_entity_types(filters.brand_id)

    return ApiResponse.success({
        "transaction_types": transaction_types,
        "entity_types": entity_types
    })
