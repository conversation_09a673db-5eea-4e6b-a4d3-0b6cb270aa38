from flask import Blueprint, request
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.transaction import (
    TransactionMasterCreateSchema,
    TransactionMasterUpdateSchema,
    TransactionMasterResponseSchema,
    TransactionMasterWithDepartmentCreateSchema,
    TransactionMasterQuerySchema,
)
from cataloging_service.domain.entities.transactions.transaction_master import TransactionMasterEntity
from cataloging_service.domain.services.transactions.transaction_master_service import TransactionMasterService
from cataloging_service.common.api_responses import ApiResponse
from cataloging_service.common.request_decorators import parse_body, parse_query
from object_registry import inject

bp = Blueprint("transaction_master", __name__)


@bp.route("/transactions", methods=["POST"])
@inject(transaction_service=TransactionMasterService)
@parse_body(TransactionMasterCreateSchema)
@atomic_operation
def create_transaction(data: TransactionMasterCreateSchema, transaction_service):
    """
    Create a new transaction master
    ---
    tags:
      - Transaction Master
    summary: Create transaction master
    description: |
      Creates a new transaction master record for financial tracking. This is the standard
      transaction creation endpoint without automatic department mapping.
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TransactionMasterCreateSchema'
          example:
            transaction_code: "TXN_ROOM_001"
            name: "Room Sale Transaction"
            property_id: "PROP123"
            entity_type: "BOOKING"
            transaction_type: "SALE"
            transaction_id: "BOOK_12345"
            operational_unit_id: "ROOM_101"
            operational_unit_type: "ROOM"
            source: "PMS"
            gl_code: "4100"
            erp_id: "ROOM_REV"
            is_merge: false
            particulars: "Room revenue transaction"
            status: "ACTIVE"
            transaction_details:
              booking_id: "BOOK_12345"
              guest_name: "John Doe"
    responses:
      201:
        description: Transaction master created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionMasterResponseSchema'
      400:
        description: Bad request - validation errors
      409:
        description: Conflict - transaction code already exists
    """
    # Create domain entity using automatic conversion
    transaction_entity = TransactionMasterEntity.model_validate(data.model_dump())

    # Use service to create transaction
    created_transaction = transaction_service.create_transaction(transaction_entity)

    # Convert to response schema using automatic conversion
    response_data = TransactionMasterResponseSchema.model_validate(created_transaction.model_dump())

    return ApiResponse.created(response_data.model_dump())


@bp.route("/transactions/with-department", methods=["POST"])
@inject(transaction_service=TransactionMasterService)
@parse_body(TransactionMasterWithDepartmentCreateSchema)
@atomic_operation
def create_transaction_with_department(data: TransactionMasterWithDepartmentCreateSchema, transaction_service):
    """
    Create a new transaction master with department mapping
    ---
    tags:
      - Transaction Master
    summary: Create transaction with department mapping
    description: |
      Creates a new transaction master with automatic department/profit center mapping.
      This endpoint looks up default mappings based on brand, transaction type, and entity type,
      then applies department-specific values (GL codes, ERP IDs, particulars).
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TransactionMasterWithDepartmentCreateSchema'
          example:
            transaction_code: "TXN_ROOM_001"
            name: "Room Sale Transaction"
            property_id: "PROP123"
            entity_type: "BOOKING"
            transaction_type: "SALE"
            transaction_id: "BOOK_12345"
            operational_unit_id: "ROOM_101"
            operational_unit_type: "ROOM"
            source: "PMS"
            status: "ACTIVE"
            brand_id: 1
            department_code: "RESTAURANT"
            profit_center_code: "DINING"
            transaction_details:
              booking_id: "BOOK_12345"
    responses:
      201:
        description: Transaction master created with department mapping
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionMasterResponseSchema'
            example:
              id: 1
              transaction_code: "TXN_ROOM_001"
              name: "Room Sale Transaction"
              property_id: "PROP123"
              gl_code: "4100"
              erp_id: "ROOM_REV"
              particulars: "Room Revenue - RESTAURANT - DINING"
              transaction_details:
                department_code: "RESTAURANT"
                profit_center_code: "DINING"
                booking_id: "BOOK_12345"
      400:
        description: Bad request - validation errors
      409:
        description: Conflict - transaction code already exists
    """
    # Extract department context
    department_code = data.department_code
    profit_center_code = data.profit_center_code
    brand_id = data.brand_id

    # Create base transaction entity (excluding department-specific fields)
    transaction_data = data.model_dump()
    transaction_data.pop('department_code', None)
    transaction_data.pop('profit_center_code', None)
    transaction_data.pop('brand_id', None)

    transaction_entity = TransactionMasterEntity.model_validate(transaction_data)

    # Use service to create transaction with department mapping
    created_transaction = transaction_service.create_transaction_with_department_mapping(
        transaction_entity, brand_id, department_code, profit_center_code
    )

    # Convert to response schema
    response_data = TransactionMasterResponseSchema.model_validate(created_transaction.model_dump())

    return ApiResponse.created(response_data.model_dump())


@bp.route("/transactions", methods=["GET"])
@inject(transaction_service=TransactionMasterService)
@parse_query(TransactionMasterQuerySchema)
def get_transactions(filters: TransactionMasterQuerySchema, transaction_service):
    """
    Get transactions with optional filters
    ---
    tags:
      - Transaction Master
    summary: List transactions with filters
    description: |
      Retrieves transaction master records with flexible filtering options.
      Supports filtering by property, department, transaction type, and status.
      Use this for financial reporting and transaction analysis.
    parameters:
      - name: property_id
        in: query
        required: false
        schema:
          type: string
        description: Filter by property ID
        example: "PROP123"
      - name: transaction_type
        in: query
        required: false
        schema:
          type: string
        description: Filter by transaction type (e.g., SALE, REFUND)
        example: "SALE"
      - name: entity_type
        in: query
        required: false
        schema:
          type: string
        description: Filter by entity type (e.g., BOOKING, ORDER)
        example: "BOOKING"
      - name: status
        in: query
        required: false
        schema:
          type: string
          enum: ["ACTIVE", "INACTIVE", "PENDING", "COMPLETED", "CANCELLED"]
        description: Filter by transaction status
        example: "ACTIVE"
      - name: department_code
        in: query
        required: false
        schema:
          type: string
        description: Filter by department code (requires property_id)
        example: "RESTAURANT"
    responses:
      200:
        description: List of transactions matching filters
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/TransactionMasterResponseSchema'
            example:
              - id: 1
                transaction_code: "TXN_ROOM_001"
                name: "Room Sale Transaction"
                property_id: "PROP123"
                entity_type: "BOOKING"
                transaction_type: "SALE"
                gl_code: "4100"
                erp_id: "ROOM_REV"
                status: "ACTIVE"
                transaction_details:
                  department_code: "RESTAURANT"
                  profit_center_code: "DINING"
      400:
        description: Bad request - invalid filter combination
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
    """
    if filters.property_id and filters.department_code:
        # Get department-specific transactions
        transactions = transaction_service.get_department_transactions(filters.property_id, filters.department_code)
    elif filters.property_id:
        # Get transactions by property
        transactions = transaction_service.get_transactions_by_property(filters.property_id, filters.status)
    elif filters.transaction_type and filters.entity_type:
        # Get transactions by type
        transactions = transaction_service.get_transactions_by_type(filters.transaction_type, filters.entity_type, filters.property_id)
    else:
        return ApiResponse.bad_request("property_id parameter is required, or transaction_type and entity_type parameters")

    # Convert to response schemas
    response_transactions = [
        TransactionMasterResponseSchema.model_validate(transaction.model_dump()).model_dump()
        for transaction in transactions
    ]

    return ApiResponse.success(response_transactions)


@bp.route("/transactions/<int:id>", methods=["GET"])
@inject(transaction_service=TransactionMasterService)
def get_transaction(id: int, transaction_service):
    """
    Get a specific transaction master
    ---
    tags:
      - Transaction Master
    summary: Get transaction by ID
    description: |
      Retrieves a specific transaction master record by its ID.
      Returns complete transaction details including department context if available.
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction master ID
        example: 1
    responses:
      200:
        description: Transaction master details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionMasterResponseSchema'
            example:
              id: 1
              transaction_code: "TXN_ROOM_001"
              name: "Room Sale Transaction"
              property_id: "PROP123"
              entity_type: "BOOKING"
              transaction_type: "SALE"
              gl_code: "4100"
              erp_id: "ROOM_REV"
              status: "ACTIVE"
              transaction_details:
                department_code: "RESTAURANT"
                profit_center_code: "DINING"
      404:
        description: Transaction not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    transaction = transaction_service.get_transaction_by_id(id)
    
    if not transaction:
        return ApiResponse.not_found("Transaction", id)

    response_data = TransactionMasterResponseSchema.model_validate(transaction.model_dump())
    return ApiResponse.success(response_data.model_dump())


@bp.route("/transactions/by-code/<transaction_code>", methods=["GET"])
@inject(transaction_service=TransactionMasterService)
def get_transaction_by_code(transaction_code: str, transaction_service):
    """
    Get a transaction by transaction code
    ---
    tags:
      - Transaction Master
    summary: Get transaction by code
    description: |
      Retrieves a specific transaction master record by its unique transaction code.
      Useful when you have the business transaction code but not the database ID.
    parameters:
      - name: transaction_code
        in: path
        required: true
        schema:
          type: string
        description: Unique transaction code
        example: "TXN_ROOM_001"
    responses:
      200:
        description: Transaction master details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionMasterResponseSchema'
            example:
              id: 1
              transaction_code: "TXN_ROOM_001"
              name: "Room Sale Transaction"
              property_id: "PROP123"
              entity_type: "BOOKING"
              transaction_type: "SALE"
              status: "ACTIVE"
      404:
        description: Transaction not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    transaction = transaction_service.get_transaction_by_code(transaction_code)
    
    if not transaction:
        return ApiResponse.not_found("Transaction", transaction_code)

    response_data = TransactionMasterResponseSchema.model_validate(transaction.model_dump())
    return ApiResponse.success(response_data.model_dump())


@bp.route("/transactions/<int:id>", methods=["PUT"])
@inject(transaction_service=TransactionMasterService)
@parse_body(TransactionMasterCreateSchema)
@atomic_operation
def replace_transaction(data: TransactionMasterCreateSchema, id: int, transaction_service):
    """
    Replace a transaction master (complete resource replacement)
    ---
    tags:
      - Transaction Master
    summary: Replace transaction master
    description: |
      Completely replaces a transaction master with new data. This is a PUT operation
      that requires all fields to be provided. Use PATCH for partial updates.
      Validates business rules including transaction code uniqueness.
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction master ID to replace
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TransactionMasterCreateSchema'
          example:
            transaction_code: "TXN_ROOM_001_UPDATED"
            name: "Updated Room Sale Transaction"
            property_id: "PROP123"
            entity_type: "BOOKING"
            transaction_type: "SALE"
            transaction_id: "BOOK_12345"
            operational_unit_id: "ROOM_101"
            operational_unit_type: "ROOM"
            source: "PMS"
            gl_code: "4100"
            erp_id: "ROOM_REV"
            is_merge: false
            particulars: "Updated room revenue transaction"
            status: "ACTIVE"
    responses:
      200:
        description: Transaction master replaced successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionMasterResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Transaction not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - transaction code already exists
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Check if transaction exists
    existing_transaction = transaction_service.get_transaction_by_id(id)
    if not existing_transaction:
        return ApiResponse.not_found("Transaction", id)

    # PUT = Complete replacement - use all fields from request
    replacement_data = data.model_dump()
    replacement_data["id"] = id

    # Create replacement entity
    replacement_entity = TransactionMasterEntity.model_validate(replacement_data)

    # Use service to update transaction
    updated_transaction = transaction_service.update_transaction(replacement_entity)

    # Convert to response schema
    response_data = TransactionMasterResponseSchema.model_validate(updated_transaction.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/transactions/<int:id>", methods=["PATCH"])
@inject(transaction_service=TransactionMasterService)
@parse_body(TransactionMasterUpdateSchema)
@atomic_operation
def patch_transaction(data: TransactionMasterUpdateSchema, id: int, transaction_service):
    """
    Partially update a transaction master
    ---
    tags:
      - Transaction Master
    summary: Update transaction master
    description: |
      Partially updates a transaction master with only the provided fields.
      This is a PATCH operation that merges new values with existing data.
      Only provided fields will be updated, others remain unchanged.
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction master ID to update
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TransactionMasterUpdateSchema'
          example:
            name: "Updated Transaction Name"
            gl_code: "4150"
            particulars: "Updated particulars"
            status: "COMPLETED"
    responses:
      200:
        description: Transaction master updated successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionMasterResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Transaction not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
      409:
        description: Conflict - transaction code already exists
        content:
          application/json:
            schema:
              $ref: '#/components/responses/Conflict'
    """
    # Get existing transaction
    existing_transaction = transaction_service.get_transaction_by_id(id)

    if not existing_transaction:
        return ApiResponse.not_found("Transaction", id)
    # PATCH = Partial update - merge only provided fields
    update_data = data.model_dump(exclude_unset=True)
    update_data["id"] = id

    # Create updated entity by merging existing with provided fields
    updated_entity = TransactionMasterEntity.model_validate(
        {**existing_transaction.model_dump(), **update_data}
    )

    # Use service to update transaction
    updated_transaction = transaction_service.update_transaction(updated_entity)

    # Convert to response schema
    response_data = TransactionMasterResponseSchema.model_validate(updated_transaction.model_dump())

    return ApiResponse.success(response_data.model_dump())


@bp.route("/transactions/<int:id>", methods=["DELETE"])
@inject(transaction_service=TransactionMasterService)
@atomic_operation
def delete_transaction(id: int, transaction_service):
    """
    Delete a transaction master
    ---
    tags:
      - Transaction Master
    summary: Delete transaction master
    description: |
      Permanently deletes a transaction master record. This action cannot be undone.
      Use with caution as this may affect financial reporting and audit trails.
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction master ID to delete
        example: 1
    responses:
      200:
        description: Transaction master deleted successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Transaction deleted successfully"
                request_id:
                  type: string
                  format: uuid
      404:
        description: Transaction not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    success = transaction_service.delete_transaction(id)
    
    if not success:
        return ApiResponse.not_found("Transaction", id)

    return ApiResponse.success({"message": "Transaction deleted successfully"})


@bp.route("/transactions/<int:id>/department", methods=["PATCH"])
@inject(transaction_service=TransactionMasterService)
@atomic_operation
def update_transaction_department(id: int, transaction_service):
    """
    Update transaction with department/profit center information
    ---
    tags:
      - Transaction Master
    summary: Update transaction department context
    description: |
      Updates a transaction master with department and profit center context.
      This adds department-specific information to the transaction details
      without affecting other transaction fields.
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          minimum: 1
        description: Transaction master ID to update
        example: 1
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: ["department_code"]
            properties:
              department_code:
                type: string
                description: Department code to associate with transaction
                example: "RESTAURANT"
              profit_center_code:
                type: string
                description: Profit center code (optional)
                example: "DINING"
          example:
            department_code: "RESTAURANT"
            profit_center_code: "DINING"
    responses:
      200:
        description: Transaction department context updated successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionMasterResponseSchema'
            example:
              id: 1
              transaction_code: "TXN_ROOM_001"
              transaction_details:
                department_code: "RESTAURANT"
                profit_center_code: "DINING"
      400:
        description: Bad request - department_code is required
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Transaction not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    department_code = request.json.get('department_code')
    profit_center_code = request.json.get('profit_center_code')

    if not department_code:
        return ApiResponse.bad_request("department_code is required")

    try:
        updated_transaction = transaction_service.update_transaction_department(
            id, department_code, profit_center_code
        )
        
        response_data = TransactionMasterResponseSchema.model_validate(updated_transaction.model_dump())
        return ApiResponse.success(response_data.model_dump())
        
    except ValueError as e:
        return ApiResponse.not_found("Transaction", id)
