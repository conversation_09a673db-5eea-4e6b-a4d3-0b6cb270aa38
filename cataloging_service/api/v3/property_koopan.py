from marshmallow import ValidationError, fields, Schema
from sqlalchemy.exc import DataError

from cataloging_service.api.schemas import BrandSchema
from cataloging_service.api.v3.schemas.property import IdNameGeneric
from cataloging_service.infrastructure.repositories import PropertyRepository
from cataloging_service.common.schema_registry import swag_schema
from core.common.api import BaseAPI
from core.common.api.api_response import response


class _LocationSchema(Schema):
    locality = fields.Nested(IdNameGeneric)
    city = fields.Nested(IdNameGeneric)


@swag_schema
class KoopanPropertySchema(Schema):
    id = fields.String()
    name = fields.Method('get_name')
    status = fields.String()
    location = fields.Nested(_LocationSchema)
    brands = fields.Nested(BrandSchema(), many=True)

    def get_name(self, obj):
        return {'new_name': obj.name}


class PropertyKoopanAPI(BaseAPI):

    def get(self):
        # TODO: 55.00 calls
        """
        ---
        description: Property Koopan API (explicitly for koopan)
        tags:
            - Koopan Property
        responses:
          200:
            description: OK
            content:
              application/json:
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/KoopanPropertySchema"
                        meta:
                            type: object
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/APIErrorSchema"
          400:
            description: Bad Request, Invalid page, Invalid Max allowed results per page
            content:
              application/json:
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                        meta:
                            type: object
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/APIErrorSchema"
          500:
            description: Unexpected exception
            content:
              application/json:
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                        meta:
                            type: object
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/APIErrorSchema"
        """
        properties = PropertyRepository().get_properties_for_koopan()

        response.data = KoopanPropertySchema(many=True).dump(properties)
        return response


@PropertyKoopanAPI.exception_handler.register([ValidationError, DataError])
def handle_validation_error(exc, context):
    response.add_error_from_exception(exc)
    response.status_code = 400
    return response


property_koopan_api_method_view = PropertyKoopanAPI.as_view('property_koopan_list')
