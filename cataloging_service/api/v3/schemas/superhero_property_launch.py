from flask_marshmallow import Schema
from marshmallow import fields, validates_schema, utils, ValidationError, validate, pre_load
from marshmallow.validate import Range, Length
from treebo_commons.money.constants import CurrencyType


class AddressSchema(Schema):
    line1 = fields.String(validate=[Length(min=1, error="line1 is required")])
    line2 = fields.String()
    city_id = fields.String()
    city_name = fields.String()
    state_name = fields.String()
    country_name = fields.String()
    pincode = fields.String()

    @validates_schema
    def validate_locality_id(self, data, **kwargs):
        if not data.get("city_name") and not data.get("city_id"):
            raise ValidationError("Please choose city.")

        elif not data.get("city_id"):
            if not data.get("country_name"):
                raise ValidationError("Please choose country.")

            if not data.get("state_name"):
                raise ValidationError("Please choose state.")

            if not data.get("city_name"):
                raise ValidationError("Please choose city.")


class RoomDetailSchema(Schema):
    room_number = fields.String(
        required=True, validate=[Length(min=1, error="room number is required")]
    )
    building_number = fields.String(required=True)
    floor_number = fields.Integer(required=True)
    room_type = fields.String(required=True)


class RoomTypeDetailSchema(Schema):
    max_adults = fields.Integer(
        required=True, validate=[Range(min=1, error="max_adults must be greater than 0")]
    )
    max_children = fields.Integer(
        required=True, validate=[Range(min=0, error="max_children must be 0 or more than 0")]
    )
    max_total = fields.Integer(
        required=True, validate=[Range(min=1, error="max_total must be greater than 0")]
    )
    room_size = fields.Integer(
        required=True, validate=[Range(min=1, error="room_size must be greater than 0")]
    )
    room_type = fields.String(required=True)
    bed_type = fields.String(validate=[Length(min=1, error="bed_type is required")])
    rate_plans = fields.List(fields.String(), required=False)


class RoomTypeTariffSchema(Schema):
    room_type = fields.String(required=True)
    single_occupancy_tariff = fields.Integer(
        required=True, validate=[Range(min=1, error="room_size must be greater than 0")]
    )
    extra_occupancy_tariff = fields.Integer(required=False)


class TenantConfigSchema(Schema):
    supported_payment_currencies = fields.List(fields.String(), required=False)
    e_reg_card = fields.String()
    external_user_id = fields.String()
    cashiering_enabled = fields.String()
    ups_enabled = fields.String()
    rate_manager_enabled = fields.String()


class ARRelatedTenantConfigSchema(Schema):
    hotel_level_accounts_receivable = fields.String()
    is_tds_override_enabled = fields.String()
    is_tds_settlement_enabled = fields.String()
    tds_settlement_percent = fields.String()


class BankDetailsSchema(Schema):
    account_name = fields.String()
    account_number = fields.String()
    ifsc_code = fields.String()
    bank = fields.String()
    branch = fields.String()


class SKUDetailsSchema(Schema):
    category = fields.String()
    category_id = fields.Integer()
    name = fields.String()
    rack_rate = fields.Integer()
    offering = fields.Dict(allow_none=True)
    frequency = fields.Dict(allow_none=True)
    hsn_sac = fields.String()


class PropertyLaunchSchema(Schema):
    name = fields.String()
    previous_name = fields.String()

    signed_date = fields.Date(format="yyyy-mm-dd")
    launch_date = fields.Date(format="yyyy-mm-dd")
    status = fields.String(default="SIGNED")

    legal_name = fields.String()
    gstin = fields.String()
    pan = fields.String()
    address = fields.Nested(AddressSchema)
    legal_address = fields.Nested(AddressSchema)
    legal_signature = fields.String()

    reception_mobile = fields.String()
    reception_landline = fields.String()
    email = fields.String()

    owner_name = fields.String(validate=[Length(min=3, error="owner_name is required")])
    owner_email = fields.Email()
    owner_phone = fields.String(validate=[Length(min=5, error="owner_phone is required")])

    secondary_owner_name = fields.String(allow_none=True)
    secondary_owner_email = fields.String(allow_none=True)
    secondary_owner_phone = fields.String(allow_none=True)

    room_type_details = fields.Nested(RoomTypeDetailSchema, many=True)
    room_type_tariffs = fields.Nested(RoomTypeTariffSchema, many=True, default=[])
    room_details = fields.Nested(RoomDetailSchema, many=True)
    star_rating = fields.Integer(allow_none=False, required=True)
    total_floors = fields.Integer(allow_none=False, required=True)
    google_maps_link = fields.String(validate=[Length(min=1, error="google_maps_link is required")])
    base_currency_code = fields.String(default=CurrencyType.INR.value)
    timezone = fields.String()
    country_code = fields.String()
    hotel_logo = fields.String()

    latitude = fields.Decimal(required=True)
    longitude = fields.Decimal(required=True)

    standard_checkin_time = fields.Time(required=True)
    free_early_checkin_time = fields.Time(required=True)
    free_late_checkout_time = fields.Time(required=True)
    standard_checkout_time = fields.Time(required=True)

    early_checkin_fee = fields.Decimal(required=True)
    late_checkout_fee = fields.Decimal(required=True)

    tenant_configs = fields.Nested(TenantConfigSchema)
    ar_related_tenant_configs = fields.Nested(ARRelatedTenantConfigSchema)
    bank_details = fields.Nested(BankDetailsSchema)
    sku_details = fields.Nested(SKUDetailsSchema, many=True, default=[])
    brand = fields.String(required=True)
    region = fields.String(required=True)

    @pre_load
    def split_room_numbers(self, in_data, **kwargs):
        room_details = in_data["room_details"]
        split_room_details = []
        for room_detail in room_details:
            room_number = room_detail.get("room_number")
            room_numbers = room_number.split(",")
            new_room_details = [{**room_detail, **{"room_number": r}} for r in room_numbers]
            split_room_details += new_room_details

        in_data["room_details"] = split_room_details
        return in_data

    @validates_schema
    def validate_timings(self, data, **kwargs):

        if not data.get("room_type_details"):
            raise ValidationError("Atleast one room type should be enabled")

        if not data.get("room_details"):
            raise ValidationError("Atleast one room should be present")

        room_numbers = [r.get("room_number") for r in data["room_details"]]

        if len(room_numbers) != len(set(room_numbers)):
            raise ValidationError("Duplicate room numbers are given")
