from flask import Blueprint, jsonify

from cataloging_service.domain import service_provider

import logging

bp = Blueprint('scripts', __name__)

logger = logging.getLogger(__name__)

ota_service = service_provider.ota_service


# cron calls this api, thi checks incompletre records and alerts
@bp.route('/alert-incomplete-mappings', methods=['POST'])
def alert_incomplete_mappings():
    # TODO: 0 calls
    ota_service.alert_incomplete_mappings()
    return jsonify(dict(status='SUCCESS')), 200


@bp.route('/alert-incomplete-otas', methods=['POST'])
def alert_incomplete_otas():
    # TODO: 0 calls
    ota_service.alert_incomplete_ota()
    return jsonify(dict(status='SUCCESS')), 200


# cron calls this api for completing mapping for CM, promo, ITS, pricing
@bp.route('/push-unirate-properties', methods=['POST'])
def push_unirate_properties():
    # TODO: 72.99 calls
    ota_service.push_unirate_properties()
    return jsonify(dict(status='SUCCESS')), 200
