from flask import Blueprint, jsonify, request

from cataloging_service.api.schemas import ComboDetailSchema, ComboSchema
from cataloging_service.api.request_objects import ComboCreateRequest, ComboEditRequest
from cataloging_service.api.validators import ComboCreateRequestValidator, ComboEditRequestValidator
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import raw_json

bp = Blueprint('combo_apis', __name__)

combo_service = service_provider.combo_service


@bp.route('/v1/sellers/<string:seller_id>/combos', methods=['POST'])
@raw_json(ComboCreateRequestValidator)
def create_combo(seller_id, parsed_request):
    """
    ---
    post:
        tags:
            - Combo
        description: Create Combo
        parameters:
            - in: path
              name: seller_id
              description: The seller_id for the combo that needs to be created
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/ComboCreateRequestValidator"
        responses:
            201:
                description: Created combo object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ComboDetailSchema"
    """
    combo_create_request = ComboCreateRequest(seller_id, parsed_request)
    created_combo = combo_service.create_combo(combo_create_request)
    return jsonify(ComboDetailSchema().dump(created_combo)), 201


@bp.route('/v1/sellers/<string:seller_id>/combos', methods=['GET'])
def get_all_combos(seller_id):
    """
    ---
    get:
        tags:
            - Combo
        description: Get All Combos
        parameters:
            - name: seller_id
              in: query
              description: Seller Id to fetch all combos for
              required: True
              schema:
                    type: string
            - name: name
              in: query
              description: Name to filter by all combos for
              required: False
              schema:
                    type: string  
            - name: food_type
              in: query
              description: Filter all combos via the food_type attribute
              required: False
              schema:
                    type: string
        description: Get All Combos
        responses:
            200:
                description: Get all Combos.
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/ComboSchema"
    """
    name = request.args.get('name', '')
    food_type = request.args.get('food_type', None)

    combos = combo_service.get_all_combos(seller_id=seller_id, name=name, food_type=food_type)
    return jsonify(ComboSchema().dump(combos, many=True)), 200


@bp.route('/v1/sellers/<string:seller_id>/combos/<int:combo_id>', methods=['GET'])
def get_combo(seller_id, combo_id):
    """
    ---
    get:
        tags:
            - Combo
        parameters:
            - name: seller_id
              in: query
              description: Seller Id to fetch combo for
              required: True
              schema:
                  type: string
            - name: combo_id
              in: query
              description: Combo Id to fetch combo for
              required: True
              schema:
                  type: int
        description: Get Combo Details
        responses:
            200:
                description: Get combo object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ComboDetailSchema"
    """
    combo = combo_service.get_combo(combo_id=combo_id)
    return jsonify(ComboDetailSchema().dump(combo)), 200


@bp.route('/v1/sellers/<string:seller_id>/combos/<int:combo_id>', methods=['PATCH'])
@raw_json(ComboEditRequestValidator)
def edit_combo(seller_id, combo_id, parsed_request):
    """
    ---
    patch:
        tags:
            - Combo
        description: Edit Combo
        parameters:
            - name: seller_id
              in: query
              description: Seller Id to edit combo for
              required: True
              schema:
                    type: string
            - name: combo_id
              in: query
              description: Combo Id to edit combo for
              required: True
              schema:
                    type: int
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/ComboEditRequestValidator"
        responses:
            200:
                description: Edited combo object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ComboDetailSchema"
    """
    combo_edit_request = ComboEditRequest(combo_id=combo_id, seller_id=seller_id, dictionary=parsed_request)
    edited_combo = combo_service.edit_combo(combo_id, seller_id, combo_edit_request)
    return jsonify(ComboDetailSchema().dump(edited_combo)), 200


@bp.route('/v1/sellers/<string:seller_id>/combos/<int:combo_id>', methods=['DELETE'])
def delete_combo(seller_id, combo_id):
    """
    ---
    delete:
        tags:
            - Combo
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to delete combo for
              required: True
              schema:
                  type: string
            - name: combo_id
              in: path
              description: Combo Id to delete combo for
              required: True
              schema:
                  type: int
        description: Delete Combo
        responses:
            204:
                description: The resource was deleted successfully.
    """
    combo_service.soft_delete_combo(combo_id=combo_id, seller_id=seller_id)
    return jsonify({}), 204
