import logging
from flask import Flask
from cataloging_service.common.api_responses import ApiResponse
from cataloging_service.exceptions import (
    CatalogingServiceException,
    ValidationException,
    ExternalClientException,
    InvalidStateIdException,
    InvalidTenantException,
    PrimaryKeyCollision,
    DatabaseLockError,
    DatabaseError,
)
from pydantic import ValidationError

logger = logging.getLogger(__name__)


def register_error_handlers(app: Flask):
    @app.errorhandler(ValidationException)
    def handle_validation_exception(e):
        logger.warning(f"ValidationException: {str(e)}")
        return ApiResponse.bad_request(
            message=e.get_error_message(),
            details=e.get_error_context()
        )

    @app.errorhandler(ValidationError)
    def handle_validation_exception(e):
        logger.warning(f"Pydantic ValidationError: {str(e)}")
        return ApiResponse.validation_error(e)

    @app.errorhandler(ExternalClientException)
    def handle_external_client_exception(e):
        logger.error(f"ExternalClientException: {e.message}")
        return ApiResponse.bad_request(
            message="External client failure",
            details=e.message
        )

    @app.errorhandler(InvalidStateIdException)
    def handle_invalid_state_id_exception(e):
        logger.warning(f"InvalidStateIdException: {str(e)}")
        return ApiResponse.bad_request(
            message="Invalid state",
            details=str(e)
        )

    @app.errorhandler(InvalidTenantException)
    def handle_invalid_tenant_exception(e):
        logger.warning("InvalidTenantException")
        return ApiResponse.bad_request(
            message="Invalid tenant",
            details=InvalidTenantException.message
        )

    @app.errorhandler(PrimaryKeyCollision)
    def handle_primary_key_collision(e):
        logger.error("PrimaryKeyCollision")
        return ApiResponse.server_error(
            exception=e,
            include_details=True
        )

    @app.errorhandler(DatabaseLockError)
    def handle_database_lock_error(e):
        logger.warning("DatabaseLockError")
        return ApiResponse.conflict(
            resource_type="Database record",
            reason=DatabaseLockError.message
        )

    @app.errorhandler(DatabaseError)
    def handle_database_error(e):
        logger.error("DatabaseError")
        return ApiResponse.server_error(
            exception=e,
            include_details=True
        )

    @app.errorhandler(CatalogingServiceException)
    def handle_cataloging_service_exception(e):
        logger.error(f"CatalogingServiceException: {str(e)}")
        return ApiResponse.bad_request(
            message=e.get_error_message(),
            details=e.get_error_context()
        )

    @app.errorhandler(ValueError)
    def handle_value_error(e):
        logger.error(f"ValueError: {str(e)}")
        return ApiResponse.bad_request(
            message=str(e)
        )

    @app.errorhandler(Exception)
    def handle_unexpected_exception(e):
        logger.exception("Unhandled exception")
        return ApiResponse.server_error(
            exception=e,
            include_details=False
        )

