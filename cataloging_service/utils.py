import datetime
import decimal
import hashlib
import json
import logging
import re
import random
import shutil
import sys
import time
import traceback
import uuid
import zipfile
from collections import defaultdict

from flask import current_app as app
from pytz import timezone
from slugify import slugify
from treebo_commons.utils import dateutils

from cataloging_service.client import client_provider
from cataloging_service.constants import constants
from cataloging_service.constants.constants import CITY_CODE_MAPPING_FOR_COST_CENTER_ID, FINANCE_SUPPORT_MAIL, \
    MAX_PROPERTY_ID_LEN
from cataloging_service.domain.enum_config_service import EnumConfigService
from cataloging_service.infrastructure.repositories import TenantConfigRepository

email_client = client_provider.email_client
logger = logging.getLogger(__name__)


class Utils:
    @staticmethod
    def generate_property_file_name(property_file_model, file_data):
        """
        :param property_file_model: Model object
        :param file_data: Uploaded file data
        :return: The file name
        The method signature should never change since this is passed as a callback in file upload field
        """
        prefix = (
            constants.COMMON_FILE_PREFIX
            if property_file_model.property is None
            else property_file_model.property.id
        )
        return "%s_%s_%s" % (prefix, property_file_model.file_type, Utils.get_epoch_time())

    @staticmethod
    def get_epoch_time():
        return int(time.time())

    @staticmethod
    def generate_md5_hash(string):
        m = hashlib.md5()
        m.update(string.encode("utf-8"))
        return m.hexdigest()

    @staticmethod
    def generate_sha1_hash(string):
        m = hashlib.sha1()
        m.update(string.encode("utf-8"))
        return m.hexdigest()

    @staticmethod
    def prefix_with_zero(string, size):
        """
        >>> [prefix_with_zero('s'*n, 7) for n in range(8)]
        ['7000000', '600000s', '50000ss', '4000sss', '300ssss', '20sssss', '1ssssss', 'sssssss']

        >>> prefix_with_zero('ssssssss', 7)
        Traceback (most recent call last):
        ...
        ValueError: Max length of property id should be 7

        :param string:
        :param size:
        :return:
        """
        if len(string) > size:
            raise ValueError("Max length of property id should be 7")
        if len(string) < size:
            difference = size - len(string)
            return str(difference) + ("0" * (difference - 1)) + string
        return string

    @staticmethod
    def generate_property_id(city, old_name):
        return Utils.prefix_with_zero(
            str(city.id)
            + str(
                Utils.generate_four_digit_hash(
                    "%s%s%s" % (city.id, old_name, Utils.get_epoch_time())
                )
            ),
            constants.MAX_PROPERTY_ID_LEN,
        )

    @staticmethod
    def generate_property_id_v2(property):
        pid = Utils.prefix_with_zero(
            str(
                Utils.generate_four_digit_hash(
                    "%s%s%s" % (property.name, property.old_name, Utils.get_epoch_time())
                )
            ),
            constants.MAX_PROPERTY_ID_LEN,
        )
        return "99" + pid[2:]

    @staticmethod
    def generate_property_id_v3():
        chars = "abcdefghijkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ23456789"
        return "".join(random.choice(chars) for _ in range(MAX_PROPERTY_ID_LEN))

    @staticmethod
    def generate_four_digit_hash(string):
        hash_value = 0
        for character in string:
            hash_value = ((hash_value * 31) + ord(character)) % 10000

        return hash_value

    @staticmethod
    def send_email(subject=None, recipients=(), body="", send_error=True):
        exception, exception_value, exception_traceback = sys.exc_info()
        if subject is None:
            subject = "CS Error: %s" % exception_value

        default_recipients = app.config[constants.CONFIG_ERROR_MAIL_RECIPIENTS]

        all_recipients = list(recipients) + default_recipients

        if send_error:
            body = body + "\n\n" + (20 * "-") + "\n" + traceback.format_exc()

        email_client.send_email(subject, all_recipients, body, None)

    @staticmethod
    def send_property_status_change_mail(property_object, recipients):
        logger.info("Sending mail to business stake holders")
        subject = "IMPORTANT - %s, %s %s" % (
            property_object,
            property_object.location.city.name,
            property_object.status,
        )
        body = (
            '<p>Team,</p><p>%s, %s status has changed to "%s". This mail is for your information.'
            "</p><p>Thanks,</p><p>The guest experience pod</p>"
            % (property_object, property_object.location.city.name, property_object.status)
        )
        email_client.send_email(subject, recipients, None, body)

    @staticmethod
    def send_recce_mail(property_object, recipients):
        logger.info("Sending recce mail for %s" % property_object)
        subject = "Schedule Recce for new upcoming property"

        body = (
            "<p>Hi Turbo/Kapil,</p><p>Please schedule recce for the signage at %s, %s ASAP. "
            "Once done, please respond to this mail with the date scheduled.</p><p>Also, please use "
            "this thread itself to manage all signage related activities for this property.</p><p>Please "
            "note - This is the only mail that will come to you in this regard. There will be no reminders/"
            "follow ups.</p><p>Thanks,</p><p>The guest experience pod</p>"
            % (property_object, property_object.location.city.name)
        )

        email_client.send_email(subject, recipients, None, body)

    @staticmethod
    def send_content_mail(property_object, recipients):
        logger.info("Sending mail to content team")
        subject = "IMPORTANT - %s, %s SIGNED" % (
            property_object,
            property_object.location.city.name,
        )

        body = (
            "<p>Team,</p><p>A new hotel - %s, %s has been Signed. All required information is available on "
            'the "Catalogue by Treebo" UI. Please initiate content creation for the hotel.</p><p>Also, please use '
            "this thread itself to manage all signage related activities for this property.</p><p>Please note - "
            "This is the only mail that will come to you in this regard. There will be no reminders/follow ups"
            ".</p><p>Thanks,</p><p>Guest Experience Pod</p>"
            % (property_object, property_object.location.city.name)
        )

        email_client.send_email(subject, recipients, None, body)

    @staticmethod
    def send_cost_center_id_change_email(property_id, old_cost_center_id, new_cost_center_id):
        logger.info("Sending mail to finance team")
        date_of_update = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if old_cost_center_id:
            subject = "Cost Center ID updated for hotel {0}".format(property_id)
            main_message = (
                "We would like to inform you that the Cost Center ID for the hotel with ID <b>{0}</b> has been updated."
            ).format(property_id)
            details = (
                "Hotel ID: {0}<br>"
                "Old Cost Center ID: {1}<br>"
                "New Cost Center ID: {2}<br>"
                "Date of Update: {3}"
            ).format(property_id, old_cost_center_id, new_cost_center_id, date_of_update)
        else:
            subject = "Cost Center ID created for hotel {0}".format(property_id)
            main_message = (
                "We would like to inform you that the Cost Center ID for the hotel with ID <b>{0}</b> has been created."
            ).format(property_id)
            details = (
                "Hotel ID: {0}<br>"
                "New Cost Center ID: {1}<br>"
                "Date of Update: {2}"
            ).format(property_id, new_cost_center_id, date_of_update)

        body = (
            "<p>Hi Team,</p>"
            "<p>{0}</p>"
            "<p>Details of the update:</p>"
            "<p>{1}</p>"
        ).format(main_message, details)

        recipients = [TenantConfigRepository().load_v2(config_name=FINANCE_SUPPORT_MAIL)[0].config_value]
        email_client.send_email(subject, recipients, None, body)

    @staticmethod
    def get_current_date_ist():
        india_tz = timezone("Asia/Kolkata")
        return datetime.datetime.now(india_tz).date()

    @staticmethod
    def get_current_time_utc():
        utc_tz = timezone("UTC")
        return datetime.datetime.now(utc_tz)

    @staticmethod
    def parse_date_time(date_time_string, format_string):
        return datetime.datetime.strptime(date_time_string, format_string)

    @staticmethod
    def format_date_time(date_object, format=constants.DEFAULT_DATE_FORMAT):
        return date_object.strftime(format)

    @staticmethod
    def is_valid_ifsc_code(code):
        pattern = re.compile(constants.IFSC_PATTERN)

        return pattern.match(code) is not None

    @staticmethod
    def is_valid_translator_param(param):
        pattern = re.compile(constants.TRANSLATOR_PATTERN, re.DOTALL | re.IGNORECASE)

        return pattern.match(param) is not None, pattern

    @staticmethod
    def is_valid_email(email):
        pattern = re.compile(constants.EMAIL_PATTERN)

        return pattern.match(email) is not None

    @staticmethod
    def strip_form_string_attributes(form):
        for attribute, attr_value in form._fields.items():
            attr = getattr(form, attribute)
            if attr and type(attr.data) == str:
                attr.data = attr.data.strip()

    @staticmethod
    def extract_zip(zip_file, directory):
        with zipfile.ZipFile(zip_file, "r") as zip_ref:
            zip_ref.extractall(directory)

    @staticmethod
    def delete_local_folder(directory):
        shutil.rmtree(directory)

    @staticmethod
    def strip_extra_whitespace(string):
        return " ".join(string.split())

    @staticmethod
    def slugify(string):
        return slugify(string)

    @staticmethod
    def get_unique_code():
        return uuid.uuid4().hex

    @staticmethod
    def get_current_month_first_date():
        cur_date = Utils.get_current_date_ist()
        return datetime.date(year=cur_date.year, month=cur_date.month, day=1)

    @staticmethod
    def get_next_month_first_date():
        cur_date = Utils.get_current_date_ist()
        month = cur_date.month
        year = cur_date.year

        if month == 12:
            month = 1
            year += 1
        else:
            month += 1

        return datetime.date(year=year, month=month, day=1)

    @staticmethod
    def is_future_date(date):
        cur_date = Utils.get_current_date_ist()
        if date > cur_date:
            return True
        return False

    @staticmethod
    def quantize_and_round_off(
        amount, decimal_places_to_round_off=4, round_off_strategy=decimal.ROUND_HALF_UP
    ):
        """
        The Standard for quantizing decimals/ floats / ints.
        This converts input to decimals and applies quantize.
        This is to provide consistency where we round off through out the code base
        """

        if not isinstance(amount, decimal.Decimal):
            amount = decimal.Decimal(amount)

        _round_off_string = "0." + "0" * decimal_places_to_round_off
        exponent_round_off = decimal.Decimal(_round_off_string)

        return amount.quantize(exponent_round_off, rounding=round_off_strategy)

    @staticmethod
    def room_type_code_generator(prefix=None):
        """
        Returns: a random generated prefixed number
        """
        created_at = datetime.datetime.utcnow()
        part1 = created_at.strftime("%d%m%y")
        part2 = created_at.strftime("%H%M%S")
        part3 = str(random.randint(0, 99))
        if prefix:
            parts = [prefix, part1, part2, part3]
        else:
            parts = [part1, part2, part3]
        return "".join(parts)

    @staticmethod
    def generate_cost_center_id(hotel_id, brand_code, region, city):
        regions = EnumConfigService().get_region_enum_values()
        if not all([brand_code, region, city, hotel_id]):
            raise ValueError("All parameters (brand_code, region, city_code, hotel_id) must be non-empty.")
        if region not in regions:
            raise ValueError("Region {0} is not configured".format(region))
        city_code_mapping = json.loads(
            TenantConfigRepository().load_v2(config_name=CITY_CODE_MAPPING_FOR_COST_CENTER_ID)[0].config_value)
        city_code = city_code_mapping[city]
        return "{}-{}-{}-{}".format(brand_code.upper(), region[0].upper(), city_code.upper(), hotel_id)


def docstring_parameter(*args, **kwargs):
    """
    Used for parameterizable docstrings. Since doc strings don't support parameters.
    """

    def dec(obj):
        obj.__doc__ = obj.__doc__.format(*args, **kwargs)
        return obj

    return dec


def group_list(values, attr):
    grouped_values = defaultdict(list)
    for value in values:
        grouped_values[getattr(value, attr)].append(value)
    return grouped_values


# converts time (1 day, 16:00:00) to hours(40:00:00)
def convert_time_to_hour_format(duration):
    if not duration:
        return None
    duration_in_seconds = duration.total_seconds()
    hours = int(duration_in_seconds // 3600)
    minutes = int(duration_in_seconds % 3600 // 60)
    seconds = int((duration_in_seconds % 3600) % 60)
    return '{:02d}:{:02d}:{:02d}'.format(hours, minutes, seconds)


def chunks(l, n):
    """Yield successive n-sized chunks from l."""
    for i in range(0, len(l), n):
        yield l[i : i + n]
