import csv
import logging

from flask_script import Option
from flask_script.commands import Command

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import Locality

logger = logging.getLogger(__name__)


class MigrateLocalityCommand(Command):
    location_repository = repo_provider.property_location_repository

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read occupancy data from'),
                   Option('--error_csv_path', dest='error_path', required=True,
                          help='The CSV file to read error occupancy data from')
                   )

    def run(self, file_path, error_path):
        self.migrate_localities(file_path, error_path)

    def migrate_localities(self, file_path, error_path):
        unsuccessful_rows = []
        with open(file_path) as locality_file:
            reader = csv.reader(locality_file)
            for row in reader:
                # logger.info('Migrating row: %s' % row)
                city_name = row[9].strip()
                location_name = row[3].strip()
                latitude = row[6].strip()
                longitude = row[7].strip()
                try:
                    if not city_name:
                        raise ValueError("City not found")
                    city = self.location_repository.get_city_by_name_strict(city_name)
                    if not city:
                        raise ValueError("City not found")

                    if not location_name and not latitude and latitude == 0.0 and not longitude and longitude == 0.0:
                        raise ValueError('Latitude and Longitude empty')

                    self.migrate_single(location_name, city, latitude, longitude)
                except Exception as e:
                    # logger.exception('Error while migrating row: %s' % row)
                    unsuccessful_rows.append((row, e))

        print('Unsuccessful rows:')
        for unsuccessful_row in unsuccessful_rows:
            print('%s | %s' % (
                unsuccessful_row[0], unsuccessful_row[1]))  # Using print because it is easier to parse later

        self.write_csv(error_path, unsuccessful_rows)

    @atomic_operation
    def migrate_single(self, location_name, city, latitude, longitude):
        locality = self.location_repository.get_locality_by_name(location_name, city.id)
        if not locality:
            locality = Locality()
            locality.name = location_name
            locality.city_id = city.id

        locality.longitude = float(longitude)
        locality.latitude = float(latitude)

        try:
            self.location_repository.persist(locality)
        except Exception:
            raise

    def write_csv(self, error_path, unsuccessful_rows):
        with open(error_path, 'w') as csvfile:
            filewriter = csv.writer(csvfile)
            try:
                for error_row in unsuccessful_rows:
                    data, error_str = error_row
                    row = data + [error_str]
                    filewriter.writerow(row)
            except Exception as err:
                print("Caught Exception. {err}".format(
                    err=err.__repr__()
                ))
                raise
