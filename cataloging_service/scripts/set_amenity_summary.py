import logging

from flask_script.commands import Command

from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.repositories import repo_provider

logger = logging.getLogger(__name__)


class SetAmenitySummary(Command):
    property_repository = repo_provider.property_repository

    def run(self):
        service_provider.property_service.set_amenity_summaries()
