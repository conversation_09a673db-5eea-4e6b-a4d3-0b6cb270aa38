from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

from cataloging_service.schemas.common import BasePydanticAPISchema


# ============================================================================
# Transaction Master Schemas
# ============================================================================

class TransactionMasterCreateSchema(BasePydanticAPISchema):
    """Schema for creating transaction master"""
    transaction_code: str = Field(..., min_length=1, max_length=100, description="Unique transaction code")
    name: str = Field(..., min_length=1, max_length=255, description="Transaction name")
    property_id: str = Field(..., description="Property ID")
    entity_type: str = Field(..., min_length=1, max_length=50, description="Entity type")
    transaction_type: str = Field(..., min_length=1, max_length=50, description="Transaction type")
    transaction_id: str = Field(..., min_length=1, max_length=100, description="Transaction identifier")
    operational_unit_id: str = Field(..., min_length=1, max_length=100, description="Operational unit ID")
    operational_unit_type: str = Field(..., min_length=1, max_length=50, description="Operational unit type")
    source: str = Field(..., min_length=1, max_length=100, description="Transaction source")
    gl_code: Optional[str] = Field(None, max_length=50, description="GL code")
    erp_id: Optional[str] = Field(None, max_length=100, description="ERP ID")
    is_merge: bool = Field(False, description="Merge flag")
    particulars: Optional[str] = Field(None, description="Transaction particulars")
    status: str = Field("ACTIVE", description="Transaction status")
    transaction_details: Dict[str, Any] = Field(default_factory=dict, description="Additional details")


class TransactionMasterUpdateSchema(BasePydanticAPISchema):
    """Schema for updating transaction master (partial updates)"""
    transaction_code: Optional[str] = Field(None, min_length=1, max_length=100)
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    entity_type: Optional[str] = Field(None, min_length=1, max_length=50)
    transaction_type: Optional[str] = Field(None, min_length=1, max_length=50)
    transaction_id: Optional[str] = Field(None, min_length=1, max_length=100)
    operational_unit_id: Optional[str] = Field(None, min_length=1, max_length=100)
    operational_unit_type: Optional[str] = Field(None, min_length=1, max_length=50)
    source: Optional[str] = Field(None, min_length=1, max_length=100)
    gl_code: Optional[str] = Field(None, max_length=50)
    erp_id: Optional[str] = Field(None, max_length=100)
    is_merge: Optional[bool] = None
    particulars: Optional[str] = None
    status: Optional[str] = None
    transaction_details: Optional[Dict[str, Any]] = None


class TransactionMasterResponseSchema(BasePydanticAPISchema):
    """Schema for transaction master response"""
    id: int
    transaction_code: str
    name: str
    property_id: str
    entity_type: str
    transaction_type: str
    transaction_id: str
    operational_unit_id: str
    operational_unit_type: str
    source: str
    gl_code: Optional[str] = None
    erp_id: Optional[str] = None
    is_merge: bool
    particulars: Optional[str] = None
    status: str
    transaction_details: Dict[str, Any]
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None


class TransactionMasterWithDepartmentCreateSchema(TransactionMasterCreateSchema):
    """Schema for creating transaction master with department context"""
    department_code: Optional[str] = Field(None, description="Department code for context")
    profit_center_code: Optional[str] = Field(None, description="Profit center code for context")
    brand_id: int = Field(..., description="Brand ID for default mapping lookup")


# ============================================================================
# Transaction Default Mapping Schemas
# ============================================================================

class TransactionDefaultMappingCreateSchema(BasePydanticAPISchema):
    """Schema for creating transaction default mapping"""
    brand_id: int = Field(..., description="Brand ID")
    transaction_type: str = Field(..., min_length=1, max_length=50, description="Transaction type")
    transaction_type_code: str = Field(..., min_length=1, max_length=50, description="Transaction type code")
    entity_type: str = Field(..., min_length=1, max_length=50, description="Entity type")
    default_gl_code: Optional[str] = Field(None, max_length=50, description="Default GL code")
    default_erp_id: Optional[str] = Field(None, max_length=100, description="Default ERP ID")
    default_particulars: Optional[str] = Field(None, description="Default particulars")
    default_is_merge: bool = Field(False, description="Default merge flag")
    transaction_details: Dict[str, Any] = Field(default_factory=dict, description="Default transaction details")
    is_active: bool = Field(True, description="Active status")


class TransactionDefaultMappingUpdateSchema(BasePydanticAPISchema):
    """Schema for updating transaction default mapping (partial updates)"""
    transaction_type: Optional[str] = Field(None, min_length=1, max_length=50)
    transaction_type_code: Optional[str] = Field(None, min_length=1, max_length=50)
    entity_type: Optional[str] = Field(None, min_length=1, max_length=50)
    default_gl_code: Optional[str] = Field(None, max_length=50)
    default_erp_id: Optional[str] = Field(None, max_length=100)
    default_particulars: Optional[str] = None
    default_is_merge: Optional[bool] = None
    transaction_details: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class TransactionDefaultMappingResponseSchema(BasePydanticAPISchema):
    """Schema for transaction default mapping response"""
    id: int
    brand_id: int
    transaction_type: str
    transaction_type_code: str
    entity_type: str
    default_gl_code: Optional[str] = None
    default_erp_id: Optional[str] = None
    default_particulars: Optional[str] = None
    default_is_merge: bool
    transaction_details: Dict[str, Any]
    is_active: bool
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None


# ============================================================================
# Bulk and Utility Schemas
# ============================================================================

class BulkTransactionDefaultMappingCreateSchema(BasePydanticAPISchema):
    """Schema for bulk creating transaction default mappings"""
    mappings: List[TransactionDefaultMappingCreateSchema] = Field(..., description="List of mappings to create")


class TransactionTypeInfoSchema(BasePydanticAPISchema):
    """Schema for transaction type information"""
    transaction_type: str
    entity_types: List[str]
    mapping_count: int


class BrandTransactionSummarySchema(BasePydanticAPISchema):
    """Schema for brand transaction summary"""
    brand_id: int
    transaction_types: List[TransactionTypeInfoSchema]
    total_mappings: int


# ============================================================================
# Query Parameter Schemas
# ============================================================================

class TransactionMasterQuerySchema(BasePydanticAPISchema):
    """Query parameters for transaction master endpoints"""
    property_id: Optional[str] = Field(None, description="Property ID to filter by")
    transaction_type: Optional[str] = Field(None, description="Transaction type to filter by")
    entity_type: Optional[str] = Field(None, description="Entity type to filter by")
    status: Optional[str] = Field(None, description="Status to filter by")
    department_code: Optional[str] = Field(None, description="Department code to filter by")


class TransactionDefaultMappingQuerySchema(BasePydanticAPISchema):
    """Query parameters for transaction default mapping endpoints"""
    brand_id: int = Field(..., description="Brand ID to filter by")
    transaction_type: Optional[str] = Field(None, description="Transaction type to filter by")
    entity_type: Optional[str] = Field(None, description="Entity type to filter by")
    active_only: bool = Field(True, description="Filter only active mappings")


class TransactionMappingLookupQuerySchema(BasePydanticAPISchema):
    """Query parameters for transaction mapping lookup"""
    brand_id: int = Field(..., description="Brand ID")
    transaction_type: str = Field(..., description="Transaction type")
    transaction_type_code: str = Field(..., description="Transaction type code")
    entity_type: str = Field(..., description="Entity type")


class TransactionTypesQuerySchema(BasePydanticAPISchema):
    """Query parameters for transaction types endpoint"""
    brand_id: int = Field(..., description="Brand ID")
