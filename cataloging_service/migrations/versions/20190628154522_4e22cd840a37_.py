"""empty message

Revision ID: 4e22cd840a37
Revises: 9ba16ff79c4c
Create Date: 2019-06-28 15:45:22.911590

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4e22cd840a37'
down_revision = '9ba16ff79c4c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room', sa.Column('linked_room_identifier', sa.INTEGER(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('room', 'linked_room_identifier')
    # ### end Alembic commands ###
