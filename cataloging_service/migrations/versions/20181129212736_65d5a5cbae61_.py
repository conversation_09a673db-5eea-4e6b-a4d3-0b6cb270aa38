"""empty message

Revision ID: 65d5a5cbae61
Revises: 1daf7c14433d
Create Date: 2018-11-29 21:27:36.910766

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '65d5a5cbae61'
down_revision = '1daf7c14433d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_detail', sa.Column('legal_seal', sa.String(length=500), nullable=True))
    op.add_column('property_detail', sa.Column('legal_signature', sa.String(length=500), nullable=True))
    op.add_column('property_detail', sa.Column('navision_code', sa.String(length=10), nullable=True))
    op.add_column('property_detail', sa.Column('sold_as_id', sa.INTEGER(), nullable=True))
    op.create_unique_constraint('_navision_code_uc', 'property_detail', ['navision_code'])
    op.create_foreign_key('property_detail_sold_as_id_fkey', 'property_detail', 'param', ['sold_as_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('property_detail_sold_as_id_fkey', 'property_detail', type_='foreignkey')
    op.drop_constraint('_navision_code_uc', 'property_detail', type_='unique')
    op.drop_column('property_detail', 'sold_as_id')
    op.drop_column('property_detail', 'navision_code')
    op.drop_column('property_detail', 'legal_signature')
    op.drop_column('property_detail', 'legal_seal')
    # ### end Alembic commands ###
