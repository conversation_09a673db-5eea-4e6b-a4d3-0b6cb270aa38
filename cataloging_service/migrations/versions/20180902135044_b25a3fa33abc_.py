"""empty message

Revision ID: b25a3fa33abc
Revises: f5fde42b0b8e
Create Date: 2018-09-02 13:50:44.226014

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b25a3fa33abc'
down_revision = 'f5fde42b0b8e'
branch_labels = None
depends_on = None

# https://stackoverflow.com/a/33617845/1790760

name = 'property_type_choices'
tmp_name = 'tmp_' + name

old_options = ('HOTEL', 'SERVICE_APARTMENT', 'RESORT', 'HOMESTAY_COTTAGE_VILLA')
new_options = sorted(old_options + ('HOMESTAY',))

old_type = sa.Enum(*old_options, name=name)
new_type = sa.Enum(*new_options, name=name)

property_detail = sa.sql.table('property_detail', sa.Column('property_type', new_type, nullable=False))


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TYPE ' + name + ' RENAME TO ' + tmp_name)

    new_type.create(op.get_bind())
    op.execute('ALTER TABLE property_detail ALTER COLUMN property_type ' +
               'TYPE ' + name + ' USING property_type::text::' + name)
    op.execute('DROP TYPE ' + tmp_name)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(property_detail.update().where(property_detail.c.property_type == 'HOMESTAY')
               .values(property_type='HOMESTAY_COTTAGE_VILLA'))

    op.execute('ALTER TYPE ' + name + ' RENAME TO ' + tmp_name)

    old_type.create(op.get_bind())
    op.execute('ALTER TABLE property_detail ALTER COLUMN property_type ' +
               'TYPE ' + name + ' USING property_type::text::' + name)
    op.execute('DROP TYPE ' + tmp_name)
    # ### end Alembic commands ###
