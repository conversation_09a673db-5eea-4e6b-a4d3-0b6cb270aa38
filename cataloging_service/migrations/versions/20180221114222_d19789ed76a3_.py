"""empty message

Revision ID: d19789ed76a3
Revises: 6b0ce4c990b9
Create Date: 2018-02-21 11:42:22.878260

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd19789ed76a3'
down_revision = '6b0ce4c990b9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room', sa.Column('room_type_config_id', sa.INTEGER(), nullable=True))
    op.create_foreign_key(None, 'room', 'room_type_configuration', ['room_type_config_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'room', type_='foreignkey')
    op.drop_column('room', 'room_type_config_id')
    # ### end Alembic commands ###
