import datetime
import logging
import re
import traceback

import requests
from flask import current_app
from flask.helpers import flash
from flask_admin.actions import action
from flask_admin.babel import gettext
from flask_admin.form import FormOpts, Select2Field
from flask_admin.form.upload import FileUploadField
from flask_admin.helpers import get_redirect_target
from flask_security import current_user
from markupsafe import Markup
from pytz import timezone, UTC
from requests import HTTPError
from treebo_commons.flask_audit.audit import AuditLogModel
from treebo_commons.multitenancy.sqlalchemy import db_engine
from wtforms import validators
from wtforms.fields import SelectField
from wtforms.validators import Regex<PERSON>, DataRequired, ValidationError

from cataloging_service.admin.custom_fields import GoogleD<PERSON><PERSON><PERSON>U<PERSON>loadField, TrimmedStringField
from cataloging_service.admin.room_amenity_ajax_loader import RoomAmenityQueryAjaxModelLoader
from cataloging_service.audit_extension import AuditedModelView, audit_ext, admin_action_resolver
from cataloging_service.constants import constants, error_codes
from cataloging_service.constants.constants import Cost<PERSON>enterConfig, DEFAULT_COST_CENTER_ID_BRAND_CODE
from cataloging_service.constants.messaging_constants import PropertyMessageActions
from cataloging_service.constants.model_choices import PropertyChoices, StandardStatusChoices
from cataloging_service.domain import service_provider
from cataloging_service.domain.enum_config_service import EnumConfigService
from cataloging_service.domain.enums import ConfigValueType
from cataloging_service.domain.migrate_to_crs import CRSMigrate
from cataloging_service.domain.property_service import PropertyService
from cataloging_service.exceptions import CatalogingServiceException, ExternalClientException
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.repositories import repo_provider, SkuRepository, TenantConfigRepository
from cataloging_service.models import SystemProperty, User, Role, Property, BankDetail, PropertyDetail, \
    GoogleDriveBaseFolder, GoogleDriveFile, Country, Cluster, State, City, MicroMarket, Locality, Location, RoomType, \
    Room, RoomTypeConfiguration, GuestFacingProcess, GuestType, Owner, AmenityPublicWashroom, AmenityElevator, \
    AmenityParking, AmenityDisableFriendly, AmenitySwimmingPool, AmenityGym, AmenitySpa, AmenityLaundry, Cuisine, \
    AmenityBreakfast, AmenityPayment, PropertyAmenity, Bar, Restaurant, BanquetHall, AmenityIntercom, AmenityHotWater, \
    AmenityTV, AmenityAC, AmenityStove, AmenityTwinBed, RoomAmenity, Landmark, Description, NeighbouringPlace, \
    Ownership, Notification, AmenityPrivateCab, AmenityHeater, TransportStation, TransportStationProperty, \
    MigrationDetail, Region, PropertyImage, PropertyVideo, CityAlias, OtaProperty, OtaPropertyMapping, OtaRoomMapping, \
    OtaRatePlanMappings, OTA, RatePlanConfiguration, RatePlan, AmenitySummary, FacilityCategory, \
    SkuCategory, FacilityCategoryMapping, PropertyLandmark, Channel, SubChannel, Application, Provider, GlobalPolicy, \
    Sku, Param, PropertySku, SkuBundle, SkuAttribute, SkuActivation, PricingPolicy, PropertyPolicyMap, PropertyPolicy, \
    PricingMapping, ProviderRoomTypeMapping, Brand, ProviderBrandMapping, PropertyBrandMapping, \
    RuptubLegalEntityDetails, SellerCategory, Seller, MenuCategory, SellerSku, RestaurantTable, Addon, RatePlanAddon, \
    NewRatePlanConfig, NewRatePlan, AvailableConfigModel, TenantConfigModel, CurrencyConversionRateModel, \
    AllowedConfigValueModel, EnumValuesModel, UserDefinedEnumModel, RoomRackRateModel, Kitchen, SellerSkuCategory, \
    GuestTypeProperty, PropertiesSkuCategories, DepartmentTemplate, ProfitCenterTemplate, Department, \
    TransactionMaster, TransactionDefaultMapping
from cataloging_service.scripts.migrate_properties import MigratePropertyCommand
from cataloging_service.utils import Utils
from cataloging_service.client.service_registry_client import ServiceRegistryClient

logger = logging.getLogger(__name__)

sku_service = service_provider.sku_service


class CustomSelectField(SelectField):

    def pre_validate(self, form):
        pass


class CatalogingServiceAdmin(AuditedModelView):
    form_excluded_columns = ('password', 'created_at', 'modified_at')
    column_display_pk = True
    column_exclude_list = ('created_at', 'modified_at', 'password')
    column_default_sort = 'id'
    can_view_details = True
    can_export = True
    create_modal = False
    edit_modal = False
    details_modal = True

    no_edit_fields = None

    def __init__(self, tenant_id, model, session, name=None, category=None, endpoint=None, url=None, static_folder=None,
                 menu_class_name=None, menu_icon_type=None, menu_icon_value=None):
        self.tenant_id = tenant_id
        super().__init__(model, session, name=name, category=category, endpoint=endpoint, url=url,
                         static_folder=static_folder, menu_class_name=menu_class_name, menu_icon_type=menu_icon_type,
                         menu_icon_value=menu_icon_value)

    def is_accessible(self):
        return current_user.is_authenticated and current_user.active

    def after_model_change(self, form, model, is_created):
        cache.expire_cache(model)

    def after_model_delete(self, model):
        cache.expire_cache(model)

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        try:
            Utils.strip_form_string_attributes(form)
            self.validate_field_change(form, model)
            form.populate_obj(model)
            self._on_model_change(form, model, False)
            self.session.commit()
        except Exception as ex:
            if not self.handle_view_exception(ex):
                flash('Failed to update record. %s' % ex, category='error')
                logger.exception('Failed to update record.')

            self.session.rollback()

            return False
        else:
            self.after_model_change(form, model, False)

        return True

    def validate_field_change(self, form, model):
        if not self.no_edit_fields:
            return True
        for attribute in self.no_edit_fields:
            current_value = getattr(form, attribute).data
            previous_value = getattr(model, attribute)
            if current_value != previous_value:
                raise validators.ValidationError('Cannot modify the field: %s' % attribute.title())

        return True


class AdminAccessView(CatalogingServiceAdmin):
    def is_accessible(self):
        return current_user.is_authenticated and current_user.is_authorized(constants.ADMIN_VIEW_ACCESSIBLE_ROLES)


class SuperUserAccessView(CatalogingServiceAdmin):
    def is_accessible(self):
        return current_user.is_authenticated and current_user.is_authorized(constants.SUPER_USER_VIEW_ACCESSIBLE_ROLES)


class NoInsertView(CatalogingServiceAdmin):
    can_create = False


class NoEditView(CatalogingServiceAdmin):
    can_edit = False


class NoDeleteView(CatalogingServiceAdmin):
    can_delete = False


class ReadOnlyView(NoInsertView, NoEditView, NoDeleteView):
    pass


class GoogleDriveFileUploadAdmin(AdminAccessView, NoEditView):
    form_excluded_columns = ('file_id', 'file_name', 'created_at', 'modified_at')
    column_filters = ('id', 'property.name', 'property.id')
    form_extra_fields = {
        'file': GoogleDriveFileUploadField(label='File',
                                           allowed_extensions=constants.FILE_UPLOAD_ALLOWED_EXTENSIONS,
                                           allow_overwrite=True, namegen=Utils.generate_property_file_name,
                                           validators=[validators.DataRequired()])
    }

    def after_model_change(self, form, model, is_created):
        if is_created and model.property:
            if service_provider.property_service.can_property_be_signed(model.property):
                service_provider.property_service.sign_property(model.property)

        return super().after_model_change(form, model, is_created)

    def on_model_delete(self, model):
        service_provider.google_drive_file_upload_service.delete_file(model.file_id)


class PropertyImageAdmin(AdminAccessView):
    form_create_rules = ('property', 'file')
    form_edit_rules = ('tag_description', 'sort_order')
    form_extra_fields = {
        'file': FileUploadField(label='File', allowed_extensions=constants.FILE_UPLOAD_EXTENSIONS,
                                allow_overwrite=True, validators=[validators.DataRequired()])
    }
    column_filters = ('property_id', 'property.name')
    column_labels = dict(path='image')

    # Folder structure :- oak/maple/mahogany/acacia/common
    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("create"))
    def create_model(self, form):
        try:
            saved_images = service_provider.property_service.upload_property_images(form.property.data.id,
                                                                                    form.file.data)
            if not saved_images:
                raise Exception('No images uploaded. Please check if the zip file has the correct structure')

        except Exception as ex:
            flash(gettext('Failed to create record. %(error)s', error=str(ex)), 'error')
            logger.exception('Failed to create record.')

            return False

        return True

    def _path_formatter(self, context, model, name):
        try:
            cdn_host = current_app.config[constants.CONFIG_CDN_HOST]
            return Markup(
                '<img src="%s%s?w=591&h=352&fm=pjpg&fit=crop" height="100" width="100"' % (cdn_host, re.sub(r'^\.', '', model.path))
            )
        except Exception:  # pylint: disable=W0703
            logger.exception("Exception occurred in outgoing_request_formatter for IncomingRequest Id: %s", model.id)
            return ""

    column_formatters = {
        'path': _path_formatter
    }


class PropertyVideoAdmin(AdminAccessView):
    form_create_rules = ('property', 'file', 'youtube_video_url')
    form_edit_rules = ('tag_description', 'sort_order', 'youtube_video_url')
    form_extra_fields = {
        'file': FileUploadField(label='File', allowed_extensions=constants.FILE_UPLOAD_EXTENSIONS,
                                id='property_file_id',
                                allow_overwrite=True)
    }
    form_overrides = {
        'youtube_video_url': TrimmedStringField
    }
    form_args = {
        'youtube_video_url': {
            'validators': [constants.YOUTUBE_VIDEO_URL_REGEXP],
        }
    }
    column_filters = ('property_id', 'property.name')
    column_labels = dict(path='video')
    create_template = 'admin/model/property_create_form.html'
    column_default_sort = ('modified_at', True)

    # Folder structure :- oak/maple/mahogany/acacia/common
    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("create"))
    def create_model(self, form):
        try:
            if not (form.file.data or form.youtube_video_url.data):
                raise validators.ValidationError("Please Provide Either Property Youtube Video URL or Upload Video.")

            if form.file.data and form.youtube_video_url.data:
                raise validators.ValidationError(
                    "Please Provide Either Property Youtube Video URL or Upload Video. Both Actions are not allowed at once."
                )
            property_videos = []
            if form.file.data:
                uploaded_videos = service_provider.property_service.upload_property_videos_on_s3(
                    form.data["property"], form.file.data
                )
                if not uploaded_videos:
                    raise Exception("No videos uploaded. Please check if the zip file has the correct structure")

                for index, s3_file in enumerate(uploaded_videos):
                    property_videos.append(
                        PropertyVideo(
                            video_url=s3_file,
                            property_id=form.data["property"].id,
                            sort_order=index,
                        )
                    )
            else:
                # Note: Only one Youtube_video_url should be present for each property
                if service_provider.property_service.is_property_having_youtube_video_url(form.data["property"]):
                    raise Exception(
                        "This property is already has mapped Youtube Video Url.You can change that mapping if required."
                    )
                property_videos.append(
                    PropertyVideo(
                        property_id=form.data["property"].id, youtube_video_url=form.data["youtube_video_url"]
                    )
                )
            service_provider.property_service.save_property_videos(property_videos)
            return True
        except Exception as ex:
            form = self._create_form_class(None, obj=None)
            flash(gettext("Failed to create record. %(error)s", error=str(ex)), "error")
            logger.exception("Failed to create record.")
            form_opts = FormOpts(widget_args=self.form_widget_args, form_rules=self._form_create_rules)
            return_url = get_redirect_target() or self.get_url(".index_view")
            self.render(
                "admin/model/create.html",
                form=form,
                form_opts=form_opts,
                return_url=return_url,
            )
            return False

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        try:
            # Note: Only one Youtube_video_url should be present for each property
            if (
                form.youtube_video_url.data
                and not model.youtube_video_url
                and service_provider.property_service.is_property_having_youtube_video_url(model)
            ):
                raise Exception(
                    "This property is already has mapped Youtube Video Url.You can change that mapping if required."
                )
            if not form.youtube_video_url.data and model.youtube_video_url:
                raise Exception(
                    "Setting empty value of Youtube Video Url for this entry is not allowed. Please delete it if you want to remove URL for this Property."
                )

            model.tag_description = form.data["tag_description"]
            model.youtube_video_url = form.data["youtube_video_url"]
            model.sort_order = form.data["sort_order"]
            service_provider.property_service.update_property_video(model)
            return True
        except Exception as e:
            form = self._edit_form_class(None, obj=None)
            flash(gettext("Failed to Edit record. %(error)s", error=str(e)), "error")
            logger.exception("Failed to Edit record.")
            form_opts = FormOpts(
                widget_args=self.form_widget_args, form_rules=self._form_edit_rules
            )
            return_url = get_redirect_target() or self.get_url(".index_view")
            self.render(
                "admin/model/edit.html",
                form=form,
                form_opts=form_opts,
                return_url=return_url,
            )
            return False

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("delete"))
    def delete_model(self, model):
        service_provider.property_service.delete_property_video(model)
        return True

    def _path_formatter(self, context, model, name):
        try:
            cdn_host = current_app.config[constants.CONFIG_CDN_HOST]
            return Markup(
                '<img src="%s%s?w=591&h=352&fm=pjpg&fit=crop" height="100" width="100"' % (cdn_host, re.sub(r'^\.', '', model.path))
            )
        except Exception:  # pylint: disable=W0703
            logger.exception("Exception occurred in outgoing_request_formatter for IncomingRequest Id: %s", model.id)
            return ""

    column_formatters = {
        'path': _path_formatter
    }


class SystemPropertyAdmin(AdminAccessView):
    column_filters = ('id', 'name')


class UserAdmin(SuperUserAccessView, NoDeleteView, NoInsertView):
    column_filters = ('id', 'email', 'roles.name', 'active')

    form_widget_args = {
        'email': {
            'readonly': True
        }
    }


class RoleAdmin(SuperUserAccessView, NoDeleteView):
    column_filters = ('id', 'name')
    form_excluded_columns = ('users', 'created_at', 'modified_at')

    no_edit_fields = ('name',)


class PropertyAdmin(AdminAccessView, NoDeleteView, NoInsertView):
    column_filters = ('id', 'hx_id', 'name', 'old_name', 'legal_name',
                      'property_detail.provider.name', 'status', 'cost_center_id')
    column_list = ('id', 'hx_id', 'status', 'name', 'old_name', 'legal_name', 'signed_date', 'contractual_launch_date',
                   'launched_date', 'churned_date', 'property_detail.provider.name', 'property_detail.provider.code',
                   'logo', 'cost_center_id', 'region', 'is_test')

    column_labels = {
        'property_detail.provider.name': 'Provider name',
        'property_detail.provider.code': 'Provider code'
    }

    form_columns = (
        'id', 'hx_id', 'name', 'status', 'legal_name', 'signed_date', 'launched_date', 'churned_date',
        'base_currency_code', 'timezone', 'country_code', 'current_business_date', 'external_hotel_id',
        'logo', 'cost_center_id', 'region', 'is_test'
    )

    form_excluded_columns = (
        'created_at', 'modified_at', 'location', 'rooms',
        'property_detail', 'google_drive_base_folder', 'google_drive_files', 'room_type_configurations',
        'guest_facing_process', 'property_amenity', 'bars', 'restaurants', 'banquet_halls', 'landmarks', 'description',
        'neighbouring_place', 'transport_station_assocs', 'ownerships', 'property_images', 'property_videos', 'property_sku',
        'property_sku_activation', 'sku_s', 'property_landmarks', 'amenity_summary')

    edit_template = 'admin/model/property_edit.html'

    page_size = 30

    form_args = dict(
        status=dict(validators=[DataRequired()]),
    )

    form_overrides = {
        "region": Select2Field,
    }

    form_widget_args = {
        'cost_center_id': {
            'readonly': True
        }
    }

    def edit_form(self, obj):
        form = super().edit_form(obj)
        region_choices = EnumConfigService().get_region_enum_values()
        form.region.choices = [(item, item) for item in region_choices]
        return form

    def create_global_skus_and_property_skus_from_crs_expense_items(self, tenant_id):
        expense_items = service_provider.property_service.get_all_expense_items(include_linked=True)
        sku_categories = sku_service.sget_all_sku_categories()
        sku_code_wise_hsn_sac = {sku_cat.code: sku_cat.hsn_sac for sku_cat in sku_categories}

        expense_item_id_to_sku = {}
        for expense_item in expense_items:
            try:
                sku = sku_service._create_new_sku(name=expense_item.name,
                                                  sku_code=expense_item.expense_item_id,
                                                  category_code=expense_item.sku_category_id.lower(),
                                                  hsn_sac=sku_code_wise_hsn_sac[expense_item.sku_category_id],
                                                  frequency=dict({"count": 1, "day_of_serving": "Check-In",
                                                                  "frequency_type": "daily"}),
                                                  offering=dict(
                                                      {"offered_quantity": 1, "offering_type": "per_guest"}),
                                                  is_expense_item_sku=True,
                                                  tax_at_room_rate=expense_item.linked)
                expense_item_id_to_sku[expense_item.expense_item_id] = sku
            except Exception as e:
                if hasattr(e, 'context') and "duplicate key" in e.context:
                    continue
                logger.exception(e)
                traceback.print_exc()
                raise

        property_ids = repo_provider.property_repository.get_property_ids(statuses=[PropertyChoices.STATUS_LIVE,
                                                                                    PropertyChoices.STATUS_SIGNED])

        for expense_item_id, sku in expense_item_id_to_sku.items():
            for property_id in property_ids:
                property_sku_dict = {"property_id": property_id, "rack_rate": 700}
                try:
                    sku_service._create_new_property_sku(property_sku_dict, sku)
                except Exception as e:
                    if hasattr(e, 'context') and "duplicate key" in e.context:
                        continue
                    logger.exception(e)
                    traceback.print_exc()
                    raise

    @action('expense_item_to_sku_sync', 'Bulk Sync expense item to sku, property sku',
            'Are you sure you want to sync the data')
    def sync_expense_item_to_property_sku(self, ids):
        self.create_global_skus_and_property_skus_from_crs_expense_items(tenant_id=self.tenant_id)

    @action('bulk_property_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.property_service.publish_properties(ids , PropertyMessageActions.BULK_UPDATE.value)

    @action('create_on_crs', 'Create on CRS', 'This will create the property on CRS. Are you sure?')
    def create_on_crs(self, ids):
        if len(ids) > 1:
            flash(message="Can only migrate one property at a time. Please select only one", category='error')
            return
        property_id = ids[0]
        try:
            crs_migrate = CRSMigrate(property_id)
            crs_migrate.start()
            flash("Property {} successfully migrated".format(property_id), 'success')
        except Exception as e:
            logger.exception(e)
            flash(str(e), 'error')

    @action('bulk_all_property_push', 'Bulk Push all properties', 'Are you sure you want to bulk push all the data?')
    def bulk_push_all_properties(self, ids):
        service_provider.property_service.publish_properties(None, PropertyMessageActions.BULK_UPDATE.value)

    @action('auto_create_sku', 'Auto create sku for property',
            'Are you sure you want to go ahead? This might take some time..')
    def bulk_auto_create_sku_for_property(self, ids):
        try:
            successful_property_ids = service_provider.property_service.auto_create_sku_for_given_properties(ids)
            failed_property_ids = set(ids) - successful_property_ids
            if failed_property_ids:
                msg = "Failed to auto create skus for property ids: {}".format(failed_property_ids)
                logger.info(msg)
                flash(msg, category='error')
            else:
                flash("Successfully Created Property Sku for selected properties", category='message')
        except Exception as e:
            logger.exception('Failed to create bundled sku {}'.format(e))
            flash(str(e), category='error')

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        try:
            Utils.strip_form_string_attributes(form)
            self.validate_field_change(form, model)
            from_status = model.status
            to_status = form.status.data
            property_status_changed = from_status != to_status
            region_change = model.region != form.region.data
            form.populate_obj(model)

            if property_status_changed:
                # Commenting out status change check to ease property onboarding-CATS-113
                # self.validate_status_change(from_status, to_status)
                if from_status == PropertyChoices.STATUS_CHURNED:
                    raise validators.ValidationError("Status of churned property cannot be changed.")

                if to_status == PropertyChoices.STATUS_LIVE:
                    status, reason = service_provider.property_service.is_property_launchable(model, self.tenant_id)
                    if not status:
                        raise validators.ValidationError(reason)
                    service_provider.property_service.publish_live_properties(
                        [model.id], is_live=True,
                        action=PropertyMessageActions.ADMIN_UPDATE.value
                    )
            if region_change:
                brand_code = model.brands[0].brand_code if (model.brands and model.brands[0].brand_code) else None
                if not brand_code:
                    brand_code = TenantConfigRepository().load_v2(config_name=DEFAULT_COST_CENTER_ID_BRAND_CODE)[0].config_value
                old_cost_center_id = model.cost_center_id
                if old_cost_center_id:
                    brand_code = old_cost_center_id.split('-')[0]
                model.cost_center_id = Utils.generate_cost_center_id(model.id, brand_code, form.region.data,
                                                               model.location.city.name)
                Utils.send_cost_center_id_change_email(property_id=model.id,
                                                       old_cost_center_id=old_cost_center_id,
                                                       new_cost_center_id=model.cost_center_id)

            self.validate_dates(model)
            self._on_model_change(form, model, False)
            self.session.commit()
        except Exception as ex:
            if not self.handle_view_exception(ex):
                flash(str(ex))

            self.session.rollback()

            return False
        else:
            self.after_model_change(form, model, False)
            if property_status_changed:
                service_provider.property_service.notify_property_status_change(model)

        return True

    def validate_status_change(self, from_status, to_status):
        # Removing the status check update to SIGNED from any status, CATS-112
        if to_status == PropertyChoices.STATUS_SIGNED:
            raise validators.ValidationError('You cannot manually set status to "SIGNED"')

        if (to_status == PropertyChoices.STATUS_NEAR_CONFIRMED) and (
                from_status != PropertyChoices.STATUS_NEAR_CONFIRMED):
            raise validators.ValidationError('You cannot go back to "Near Confirmed"')

        if (to_status == PropertyChoices.STATUS_LIVE) and (from_status != PropertyChoices.STATUS_SIGNED):
            raise validators.ValidationError('Only "SIGNED" properties can be made Live')

        if (to_status == PropertyChoices.STATUS_DROPPED) and (from_status != PropertyChoices.STATUS_SIGNED):
            raise validators.ValidationError('Only "SIGNED" properties can be dropped')

        if (to_status == PropertyChoices.STATUS_NOT_SIGNING) and (
                from_status != PropertyChoices.STATUS_NEAR_CONFIRMED):
            raise validators.ValidationError('Only "Near Confirmed" properties can be "Not Signed"')

        if (to_status == PropertyChoices.STATUS_CHURNED) and (from_status != PropertyChoices.STATUS_LIVE):
            raise validators.ValidationError('Only "Live" properties can be churned')

    def validate_dates(self, property_object):
        try:
            PropertyService.validate_and_sanitize_property_dates(property_object)
        except CatalogingServiceException as exception:
            message = exception.get_error_message()
            raise validators.ValidationError(message)


class BankDetailAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'account_number', 'ifsc_code', 'branch', 'bank', 'property_detail.property.name',
                      'property_detail.property.id')
    form_args = dict(
        ifsc_code=dict(validators=[Regexp(constants.IFSC_PATTERN)]),
        swift_code=dict(validators=[Regexp(constants.SWIFT_PATTERN)]),
        property_detail=dict(validators=[DataRequired()])
    )

    no_edit_fields = ('property_detail',)



class PropertyDetailAdmin(AdminAccessView, NoDeleteView):
    no_edit_fields = ('property',)
    column_filters = ('id', 'property.name', 'property.id', 'provider.name', 'provider.code', 'sold_as.value')
    form_excluded_columns = ('created_at', 'modified_at')
    form_overrides = {
        'legal_signature': FileUploadField
    }

    legal_signature_field_dict = {
        'label': 'legal signature',
        'base_path': '.',
        'allow_overwrite': True,
        'allowed_extensions': constants.IMAGE_UPLOAD_ALLOWED_EXTENSIONS
    }

    form_args = dict(
        sold_as=dict(query_factory=lambda: db_engine.get_session().query(Param).
                     filter_by(entity=PropertyDetail.__name__, field='sold_as_id', validate=True),
                     validators=[DataRequired()]),
        pan=dict(validators=[Regexp(constants.PAN_PATTERN, message="give a valid pan number, length 10 characters")]),
        navision_code=dict(validators=[
            Regexp(constants.NAVISION_CODE_PATTERN, message="give a valid navision code, minimum 6 characters")]),
        legal_signature=legal_signature_field_dict
    )

    def validate_gstin_first_two_digits_match_state_code(self, gstin, state_code):
        if int(gstin[:2]) != state_code:
            flash('First two digits of gstin should match state code.', 'error')
            return False
        return True

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("create"))
    def create_model(self, form):
        if form.data['gstin']:
            gstin = form.data['gstin']
            if not form.data['property'].location.legal_city:
                flash('Please provide a valid legal city.', 'error')
                return
            state_code = form.data['property'].location.legal_city.state.code
            if not self.validate_gstin_first_two_digits_match_state_code(gstin, state_code):
                return
        if (form.data['is_msme'] and not form.data['msme_number']) or \
                (form.data['msme_number'] and not form.data['is_msme']):
            flash("Please select Is Msme or provide Msme number or don't provide Msme details at all", 'error')
            return
        return super(PropertyDetailAdmin, self).create_model(form)

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        if form.data['gstin']:
            gstin = form.data['gstin']
            if not form.data['property'].location.legal_city:
                flash('Please provide a valid legal city.', 'error')
                return
            state_code = form.data['property'].location.legal_city.state.code
            if not self.validate_gstin_first_two_digits_match_state_code(gstin, state_code):
                return
        if (form.data['is_msme'] and not form.data['msme_number']) or \
                (form.data['msme_number'] and not form.data['is_msme']):
            flash("Please select Is Msme or provide Msme number or don't provide Msme details at all", 'error')
            return
        return super(PropertyDetailAdmin, self).update_model(form, model)

    def _legal_signature_formatter(self, context, model, name):
        try:
            cdn_host = current_app.config[constants.CONFIG_CDN_HOST]
            if model.legal_signature:
                return Markup(
                    '<img src="%s%s?w=591&h=352&fm=pjpg&fit=crop" height="100" width="100">' % (cdn_host, model.legal_signature)
                )
            return Markup(
                '<img src="" height="100" width="100">'
            )
        except Exception as e:  # pylint: disable=W0703
            logger.exception("Exception occurred in outgoing_request_formatter for IncomingRequest Id: %s", model.id)
            return ""

    column_formatters = {
        'legal_signature': _legal_signature_formatter
    }

    def handle_image_upload(self, form, tag=None):
        try:
            saved_image_path = service_provider.property_service.upload_image(getattr(form, tag).data)

            if not saved_image_path:
                raise Exception('No images uploaded. Please check if the correct config was used.')
            return saved_image_path
        except Exception as e:
            raise e

    def on_model_change(self, form, model, is_created):
        try:
            legal_signature_data = getattr(form, 'legal_signature').data
            # no change in the legal signature field
            if not type(legal_signature_data) is str:
                path = self.handle_image_upload(form, tag='legal_signature')
                model.legal_signature = path
            else:
                logger.info("no change in the legal_signature field value, ignoring upload")

            self._validate_reseller_changes(form)

            self._create_seller_type(form, is_created)
        except Exception as e:
            logger.exception(e)
            raise ValidationError(e)

    def _create_seller_type(self, form, is_created):
        # if model is created or if property is not LIVE or if property details are populated without sold_as field, in
        # those cases from_seller and to_seller is set to the selected sold_as in the admin form
        if is_created or form.property.data.status != PropertyChoices.STATUS_LIVE or not form.sold_as.object_data:
            service_provider.seller_type_history_service.create_seller_type_history(form.property.data.id,
                                                                                    form.sold_as.data.value,
                                                                                    form.sold_as.data.value,
                                                                                    Utils.get_current_date_ist())
        elif form.sold_as.object_data.value != form.sold_as.data.value:
            service_provider.seller_type_history_service.create_seller_type_history(form.property.data.id,
                                                                                    form.sold_as.object_data.value,
                                                                                    form.sold_as.data.value,
                                                                                    Utils.get_current_date_ist()
                                                                                    + datetime.timedelta(days=1))

    def _validate_reseller_changes(self, form):
        error_list = []
        sold_as_current_value = form.sold_as.data.value
        if sold_as_current_value == constants.RESELLER:
            if not form.gstin.data:
                error_list.append('gstin can\'t be null for RESELLER property')
            if not form.legal_signature.data:
                error_list.append('legal_signature can\'t be null for RESELLER property')
            if not form.pan.data:
                error_list.append('pan can\'t be null for RESELLER property')
            if not form.property.data.legal_name:
                error_list.append('legal_name can\'t be null for RESELLER property')

            if not form.property.data.location:
                error_list.append('location info can\'t be null for RESELLER property')
        if error_list:
            raise Exception(error_list)


class GoogleDriveBaseFolderAdmin(AdminAccessView, ReadOnlyView):
    column_filters = ('id', 'property.name', 'property.id')


class CountryAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name')
    form_excluded_columns = ('created_at', 'modified_at', 'states')


class ClusterAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name')
    form_excluded_columns = ('created_at', 'modified_at', 'cities')
    form_args = dict(
        region=dict(validators=[DataRequired()])
    )


class RegionAdmin(AdminAccessView, NoDeleteView, NoEditView):
    column_filters = ('id', 'name')
    form_excluded_columns = ('created_at', 'modified_at', 'clusters')


class StateAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name', 'country.name')
    form_excluded_columns = ('created_at', 'modified_at', 'cities')


class CityAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name', 'state.name')
    form_excluded_columns = ('created_at', 'modified_at', 'micro_markets', 'localities', 'locations')

    @action('bulk_city_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.room_service.publish_cities(ids)


class CityAliasAdmin(AdminAccessView):
    column_filters = ('id', 'name', 'city.name')


class MicroMarketAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name', 'city.name')
    form_excluded_columns = ('created_at', 'modified_at', 'localities', 'locations')


class LocalityAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name', 'city.name')
    form_excluded_columns = ('created_at', 'modified_at', 'locations')


class LocationAdmin(AdminAccessView, NoDeleteView):
    column_list = ('property', 'micro_market', 'locality', 'city', 'legal_city', 'pincode', 'legal_pincode', 'latitude',
                   'longitude', 'postal_address', 'legal_address', 'maps_link')
    column_filters = ('id', 'property.name', 'city.name')
    no_edit_fields = ('property',)


class RoomTypeAdmin(AdminAccessView, NoDeleteView):
    form_excluded_columns = ('rooms', 'room_type_configurations', 'created_at', 'modified_at')
    no_edit_fields = ('type',)

    @action('bulk_roomtype_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.room_service.publish_room_types(ids)


class RoomAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'property.name', 'property.id')
    form_excluded_columns = ('amenity', 'created_at', 'modified_at', 'room_type_config', 'size',
                             'linked_room_identifier')
    form_extra_fields = {
        'linked_rooms': CustomSelectField('Linked Rooms', choices=[('', None)], default=0)
    }
    no_edit_fields = ('property', 'room_type')

    @action('bulk_room_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.room_service.publish_rooms(ids)

    @action('clone_room', 'Clone Room', 'Are you sure you want to clone the room?')
    def clone_room(self, room_ids):
        if len(room_ids) > 1:
            flash("Select only one room", category='error')
            return
        service_provider.room_service.clone_room(room_ids[0])

    def on_form_prefill(self, form, id):
        active_rooms = service_provider.room_service.get_property_rooms(form.data['property'])
        choices = [('', None)]
        for room in active_rooms:
            if id == str(room.id) or not room.is_active:
                continue
            else:
                choices.append((room.id, (room.id, room.room_number, room.room_type.type)))
        form.linked_rooms.choices = choices

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        try:
            self.on_form_prefill(form=form, id=None)
            if not model.is_active and form.data['is_active']:
                raise validators.ValidationError("You cannot mark a room active once it is inactive")
            elif not form.data['is_active']:
                if not form.data['linked_rooms']:
                    model.linked_room_identifier = None
                else:
                    model.linked_room_identifier = form.data['linked_rooms']
            else:
                model.linked_room_identifier = None
            Utils.strip_form_string_attributes(form)
            self.validate_field_change(form, model)
            old_room_type_config = model.room_type_config
            form.populate_obj(model)
            self.on_model_change(form, model, False, old_room_type_config)
            self.session.commit()
        except Exception as ex:
            if not self.handle_view_exception(ex):
                flash('Failed to update record. %s' % ex, category='error')
                logger.exception('Failed to update record.')

            self.session.rollback()

            return False
        else:
            self.after_model_change(form, model, False)

        return True

    def on_model_change(self, form, model, is_created, old_room_type_config=None):
        room_config = repo_provider.property_repository.get_property_room_type_configuration_by_room_type(
            model.property.id, model.room_type.id)
        min_size_map = repo_provider.property_repository.get_property_min_room_size_map(model.property.id)
        if not room_config:
            raise validators.ValidationError(
                'Room type %s not present for hotel %s' % (model.room_type, model.property))
        model.room_type_config = room_config
        room_config.modified_at = Utils.get_current_time_utc()
        room_config.min_room_size = min_size_map.get(room_config.room_type_id, None)
        self.session.add(room_config)

        if not is_created and old_room_type_config and (model.room_type_config != old_room_type_config):
            old_room_type_config.modified_at = Utils.get_current_time_utc()
            old_room_type_config.min_room_size = min_size_map.get(old_room_type_config.room_type_id, None)
            self.session.add(old_room_type_config)
        return True


class RoomTypeConfigurationAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name', 'provider.name', 'provider.code')
    form_excluded_columns = ('created_at', 'modified_at', 'property_images', 'property_rooms')

    no_edit_fields = ['room_type']

    form_widget_args = {
        'min_room_size': {
            'disabled': True
        }
    }

    def on_model_change(self, form, model, is_created):
        super(RoomTypeConfigurationAdmin, self).on_model_change(form, model, is_created)

        try:
            room_service = service_provider.room_service
            room_service.validate_not_null(model.adults)
            room_service.validate_not_null(model.children)
            room_service.validate_room_type_config(model.adults, model.children, model.max_total,
                                                                    model.min_occupancy)
        except CatalogingServiceException as e:
            raise validators.ValidationError(e.get_error_message())

    def on_model_delete(self, model):
        super(RoomTypeConfigurationAdmin, self).on_model_delete(model)
        if model.property_rooms:
            raise validators.ValidationError('Cannot delete config with associated rooms')
        if model.property_images:
            raise validators.ValidationError('Cannot delete Config with associated image')

    @action('bulk_room_config_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.room_service.publish_room_configs(ids)


class GuestFacingAdmin(AdminAccessView):
    no_edit_fields = ('property',)
    column_filters = ('id', 'property.id', 'property.name')

    def on_model_change(self, form, model, is_created):
        if model.free_early_checkin and model.free_early_checkin < datetime.time(6, 0, 0):
            raise validators.ValidationError("Early-checkin-time should be greater than or equal to 6am")
        if model.switch_over_time and model.switch_over_time > model.free_early_checkin:
            raise validators.ValidationError("switch-over-time cannot be greater than early-checkin-time")

        if model.switch_over_time and model.system_freeze_time and model.switch_over_time > model.system_freeze_time:
            raise validators.ValidationError("switch-over-time cannot be greater than system freeze time")


class GuestTypeAdmin(AdminAccessView):
    column_filters = ('id', 'type')
    form_excluded_columns = ('properties', 'created_at', 'modified_at')
    no_edit_fields = ('type',)


class GuestTypePropertyAdmin(AdminAccessView, NoEditView):
    column_filters = ('guest_type.type', 'property.id', 'property.name')
    column_list = ['id', 'guest_type', 'property']
    form_columns = ['guest_type', 'property']

    column_labels = {
        'guest_type': 'Guest Type',
        'property': 'Property'
    }


class OwnerAdmin(AdminAccessView):
    column_filters = ('id', 'first_name', 'ownerships.property.id', 'ownerships.property.name')
    form_excluded_columns = ('ownerships', 'created_at', 'modified_at')

    form_args = dict(
        email=dict(validators=[Regexp(constants.EMAIL_PATTERN)])
    )


class CommonAmenityAdmin(AdminAccessView):
    column_filters = ('id', 'property_amenity.property.name', 'property_amenity.property.id')
    no_edit_fields = ('property_amenity',)
    form_args = dict(
        property_amenity=dict(validators=[DataRequired()])
    )

    def on_model_change(self, form, model, is_created):
        super(CommonAmenityAdmin, self).on_model_change(form, model, is_created)
        service_provider.property_service.set_amenity_summary(model.property_amenity.property)

    def after_model_delete(self, model):
        super(CommonAmenityAdmin, self).after_model_delete(model)
        service_provider.property_service.set_amenity_summary(model.property_amenity.property)


class CuisineAdmin(AdminAccessView):
    column_filters = ('id', 'name')
    form_excluded_columns = ('breakfasts', 'restaurants', 'created_at', 'modified_at')
    no_edit_fields = ('name',)


class PropertyAmenityAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'property.id', 'property.name')
    form_excluded_columns = ('created_at', 'modified_at', 'public_washroom', 'elevator', 'parking', 'private_cab',
                             'disable_friendly', 'swimming_pool', 'gym', 'spa', 'laundry', 'breakfast', 'payment')
    no_edit_fields = ('property',)

    @action('bulk_property_amenity_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.property_service.publish_property_amenities(ids)

    def on_model_change(self, form, model, is_created):
        super(PropertyAmenityAdmin, self).on_model_change(form, model, is_created)
        service_provider.property_service.set_amenity_summary(model.property)

    def after_model_delete(self, model):
        super(PropertyAmenityAdmin, self).after_model_delete(model)
        service_provider.property_service.set_amenity_summary(model.property)


class BarAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name')
    no_edit_fields = ('property',)

    @action('bulk_bar_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.property_service.publish_bars(ids)

    def on_model_change(self, form, model, is_created):
        super(BarAdmin, self).on_model_change(form, model, is_created)
        service_provider.property_service.set_amenity_summary(model.property)

    def after_model_delete(self, model):
        super(BarAdmin, self).after_model_delete(model)
        service_provider.property_service.set_amenity_summary(model.property)


class RestaurantAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name')
    no_edit_fields = ('property',)

    @action('bulk_restaurant_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.property_service.publish_restaurants(ids)

    def on_model_change(self, form, model, is_created):
        super(RestaurantAdmin, self).on_model_change(form, model, is_created)
        service_provider.property_service.set_amenity_summary(model.property)

    def after_model_delete(self, model):
        super(RestaurantAdmin, self).after_model_delete(model)
        service_provider.property_service.set_amenity_summary(model.property)


class BanquetHallAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name')
    no_edit_fields = ('property',)

    @action('bulk_hall_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.property_service.publish_banquet_halls(ids)

    def on_model_change(self, form, model, is_created):
        super(BanquetHallAdmin, self).on_model_change(form, model, is_created)
        service_provider.property_service.set_amenity_summary(model.property)

    def after_model_delete(self, model):
        super(BanquetHallAdmin, self).after_model_delete(model)
        service_provider.property_service.set_amenity_summary(model.property)


class RoomAmenityCommonAdmin(AdminAccessView):
    column_filters = (
        'id', 'room_amenity.room.property.id', 'room_amenity.room.property.name', 'room_amenity.room.room_number')
    no_edit_fields = ('room_amenity',)
    form_ajax_refs = {
        'room_amenity': RoomAmenityQueryAjaxModelLoader(
            'room_amenity',
            db_engine.get_scoped_session(),
            RoomAmenity,
            fields=['room'],
            page_size=10,
            order_by='room_id',
        )
    }
    form_args = dict(
        room_amenity=dict(validators=[DataRequired()])
    )

    def on_model_change(self, form, model, is_created):
        super(RoomAmenityCommonAdmin, self).on_model_change(form, model, is_created)
        service_provider.property_service.set_amenity_summary(model.room_amenity.room.property)

    def after_model_delete(self, model):
        super(RoomAmenityCommonAdmin, self).after_model_delete(model)
        service_provider.property_service.set_amenity_summary(model.room_amenity.room.property)


class RoomAmenityAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'room.room_number', 'room.property.id', 'room.property.name')
    form_excluded_columns = (
        'created_at', 'modified_at', 'heater', 'twin_bed', 'intercom', 'hot_water', 'tv', 'ac', 'stove')
    no_edit_fields = ('room',)

    @action('bulk_room_amenity_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.room_service.publish_room_amenities(ids)

    def on_model_change(self, form, model, is_created):
        super(RoomAmenityAdmin, self).on_model_change(form, model, is_created)
        service_provider.property_service.set_amenity_summary(model.room.property)

    def after_model_delete(self, model):
        super(RoomAmenityAdmin, self).after_model_delete(model)
        service_provider.property_service.set_amenity_summary(model.room.property)


class LandmarkAdmin(AdminAccessView, NoEditView):
    column_filters = ('id', 'name')
    column_list = ('id', 'name', 'latitude', 'longitude')
    form_excluded_columns = (
        'property', 'type', 'distance_from_property', 'property_direction', 'hatchback_cab_fare', 'sedan_cab_fare',
        'property_landmarks', 'created_at', 'modified_at')

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("create"))
    def create_model(self, form):
        try:
            self.is_duplicate_landmark(form.data['latitude'], form.data['longitude'], form.data['name'])
        except Exception as e:
            flash(e, 'error')
            return False
        return super(LandmarkAdmin, self).create_model(form)

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        try:
            self.is_duplicate_landmark(form.data['latitude'], form.data['longitude'], form.data['name'])
        except Exception as e:
            flash(e, 'error')
            return False
        return super(LandmarkAdmin, self).update_model(form, model)

    def is_duplicate_landmark(self, latitude, longitude, name):
        if service_provider.property_service.is_duplicate_landmark(name, latitude, longitude):
            raise validators.ValidationError('Duplicate landmark')


class PropertyLandmarkAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name', 'landmark.name')
    no_edit_fields = ('property', 'landmark')


class DescriptionAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name')
    no_edit_fields = ('property',)


class NeighbouringPlaceAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name')
    no_edit_fields = ('property',)


class OwnershipAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name', 'owner.id', 'owner.first_name')
    no_edit_fields = ('property', 'owner')


class TransportStationPropertyAdmin(AdminAccessView):
    column_filters = ('id', 'property.id', 'property.name', 'transport_station.id', 'transport_station.name')
    no_edit_fields = ('property', 'transport_station')


class TransportStationAdmin(AdminAccessView):
    column_filters = ('id', 'name')
    form_excluded_columns = ('transport_station_assocs', 'created_at', 'modified_at')


class NotificationAdmin(SuperUserAccessView):
    column_filters = ('id', 'type', 'receivers')


class MigrationAdmin(SuperUserAccessView, NoEditView):
    migration_command = MigratePropertyCommand()
    form_excluded_columns = ('created_at', 'modified_at', 'is_success', 'error_message')

    def on_model_change(self, form, model, is_created):
        try:
            self.migration_command.migrate(model.migration_sheet_name, model.old_hotel)
            model.is_success = True
        except Exception as exception:
            logger.error(exception)
            model.is_success = False
            error_message = '%s' % exception
            flash(traceback.format_exc(), 'error')
            model.error_message = (error_message[constants.ERROR_MSG_LENGTH - 1]) if len(
                error_message) > constants.ERROR_MSG_LENGTH else error_message
            repo_provider.property_repository.persist(model)
            repo_provider.property_repository.session().commit()
            raise validators.ValidationError(error_message)


class OtaPropertyAdmin(SuperUserAccessView, NoDeleteView):
    no_edit_fields = ('ota', 'property')
    inline_models = (OtaPropertyMapping, OtaRoomMapping, OtaRatePlanMappings)
    column_filters = ('property_id', 'property.name',)

    column_list = ('property', 'ota', 'rcs_push_complete', 'rcs_callback_complete', 'unirate_push_complete',
                   'promo_push_complete', 'rate_push_complete', 'inventory_push_complete', 'rcs_push_time',
                   'rcs_callback_time', 'unirate_push_time')

    column_labels = dict(rcs_push_complete="ITS Push Complete",
                         rcs_callback_complete="ITS Callback Complete",
                         rcs_push_time="ITS Push Time",
                         rcs_callback_time="ITS Callback Time")

    form_rules = ('property', 'ota', 'hotel_mappings', 'room_mappings', 'rate_plan_mappings',)

    form_excluded_columns = ('created_at', 'modified_at', 'rcs_push_complete', 'rcs_callback_complete',
                             'unirate_push_complete', 'rcs_push_time', 'rcs_callback_time', 'unirate_push_time',
                             'rate_push_complete', 'promo_push_complete', 'inventory_push_complete',)

    def on_model_change(self, form, model, is_created):
        super(OtaPropertyAdmin, self).on_model_change(form, model, is_created)

        try:
            # check to see if property is a treebo property
            if not service_provider.ota_service.check_if_property_is_provided_by_treebo(model):
                raise validators.ValidationError('Not a treebo property')
            if is_created:
                if model.property.status != PropertyChoices.STATUS_LIVE:
                    raise CatalogingServiceException(error_codes.PROPERTY_NOT_LIVE, context='The property is not LIVE')
        except CatalogingServiceException as e:
            logger.exception('Error in ota configuration')
            raise validators.ValidationError(e.context)

    @action('push_ota_mappings', 'Push OTA Mappings', 'Are you sure you want to push OTA Mappings?')
    def push_details(self, ota_property_ids):
        for ota_property_id in ota_property_ids:
            try:
                ota_property = repo_provider.property_repository.get_ota_property_by_id(ota_property_id)
                ota_property.rate_push_complete = False
                ota_property.promo_push_complete = False
                ota_property.inventory_push_complete = False
                ota_property.rcs_push_complete = False
                ota_property.rcs_callback_complete = False
                ota_property.unirate_push_complete = False
                ota_property.rcs_push_time = None
                ota_property.rcs_callback_time = None
                ota_property.unirate_push_time = None
                repo_provider.property_repository.persist(ota_property)
                repo_provider.property_repository.session().commit()
                service_provider.ota_service.validate_ota_property(ota_property)
                # Push to RCS not CM
                service_provider.ota_service.push_ota_mappings(ota_property, True, False)
            except CatalogingServiceException as e:
                logger.exception('Error while pushing mapping details')
                flash(('Error in %s - %s: ' + e.context) % (ota_property.ota, ota_property.property.name),
                      category='error')


class RatePlanConfigurationAdmin(SuperUserAccessView, NoEditView):
    column_filters = ('rate_plan.plan', 'property.name', 'property_id')


class RatePlanAdmin(SuperUserAccessView, NoDeleteView):
    form_excluded_columns = ('created_at', 'modified_at', 'rate_plan_configurations')
    no_edit_fields = ('plan',)


class OtaAdmin(SuperUserAccessView, NoDeleteView):
    form_excluded_columns = ('created_at', 'modified_at', 'rcs_push_complete', 'rcs_callback_complete',
                             'unirate_push_complete', 'rcs_push_time', 'rcs_callback_time', 'unirate_push_time')

    no_edit_fields = ('name', 'ota_code', 'mm_ota_code', 'unirate_ota_code')
    column_labels = dict(rcs_push_complete="ITS Push Complete", rcs_callback_complete="ITS Callback Complete",
                         rcs_push_time="ITS Push Time", rcs_callback_time="ITS Callback Time"
                         )

    def on_model_change(self, form, model, is_created):
        super(OtaAdmin, self).on_model_change(form, model, is_created)
        model.rcs_push_complete = False
        model.rcs_callback_complete = False
        model.unirate_push_complete = False

    @action('push_ota_details', 'Push OTA Details', 'Are you sure you want to push the OTA details?')
    def push_ota_details(self, ota_ids):
        for ota_id in ota_ids:
            try:
                ota = repo_provider.property_repository.get_ota_by_id(ota_id)
                service_provider.ota_service.push_ota_details(ota, True, False)
            except CatalogingServiceException as e:
                logger.exception('Error after creating/editing OTA')
                flash(e.context, category='error')


class AmenitySummaryAdmin(SuperUserAccessView, ReadOnlyView):
    column_filters = ('property_id', 'property.name')

    @action('bulk_summary_push', 'Bulk summary push', 'Are you sure you want to push amenity summaries?')
    def bulk_push(self, summary_ids):
        service_provider.property_service.publish_amenity_summaries(summary_ids)


class FacilityCategoryAdmin(SuperUserAccessView, NoEditView, NoDeleteView):
    form_excluded_columns = ('created_at', 'modified_at', 'facilities')

    def on_model_change(self, form, model, is_created):
        super(FacilityCategoryAdmin, self).on_model_change(form, model, is_created)

        if not is_created:
            service_provider.property_service.set_amenity_summaries()


class FacilityCategoryMappingAdmin(SuperUserAccessView):
    form_excluded_columns = ('created_at', 'modified_at', 'facility_name')

    @action('summarize_amenities', 'Summarize Property Amenities',
            'Are you sure you want to go ahead? This might take some time..')
    def summarize_amenities(self, summary_ids):
        service_provider.property_service.set_amenity_summaries()

    def on_model_change(self, form, model, is_created):
        super(FacilityCategoryMappingAdmin, self).on_model_change(form, model, is_created)
        model.facility_name = form.amenity_name.data

    choices = [('fridge', 'fridge'), ('balcony', 'balcony'), ('kitchenette', 'kitchenette'),
               ('kitchenette_utensils', 'kitchenette_utensils'), ('king_bed', 'king_bed'),
               ('queen_bed', 'queen_bed'), ('single_beds', 'single_beds'), ('cupboards', 'cupboards'),
               ('safety_locker', 'safety_locker'),
               ('microwave', 'microwave'), ('luggage_shelf', 'luggage_shelf'),
               ('study_table_chair', 'study_table_chair'), ('sofa_chair', 'sofa_chair'),
               ('coffee_table', 'coffee_table'), ('other_furniture', 'other_furniture'),
               ('smoking_room', 'smoking_room'), ('bath_tub', 'bath_tub'), ('shower_curtain', 'shower_curtain'),
               ('smoke_alarm', 'smoke_alarm'), ('shower_cabinets', 'shower_cabinets'), ('living_room', 'living_room'),
               ('dining_table', 'dining_table'), ('windows', 'windows'),
               ('complimentary_toiletries', 'complimentary_toiletries'),
               ('fan', 'fan'), ('lock', 'lock'), ('bucket_mug', 'bucket_mug'),
               ('mosquito_repellent', 'mosquito_repellent'), ('heater', 'heater'), ('twin_bed', 'twin_bed'),
               ('intercom', 'intercom'), ('geyser', 'geyser'), ('flat_screen_tv', 'flat_screen_tv'),
               ('ac_room', 'ac_room'), ('stove', 'stove'),
               ('public_washroom', 'public_washroom'), ('elevator', 'elevator'), ('lobby_ac', 'lobby_ac'),
               ('lobby_furniture', 'lobby_furniture'), ('lobby_smoke_alarm', 'lobby_smoke_alarm'),
               ('24_hour_security', '24_hour_security'), ('pantry', 'pantry'), ('luggage_storage', 'luggage_storage'),
               ('travel_desk', 'travel_desk'), ('room_service', 'room_service'),
               ('roof_top_restaurant', 'roof_top_restaurant'),
               ('pool_table', 'pool_table'), ('pet_friendly', 'pet_friendly'), ('parking', 'parking'),
               ('cab_service', 'cab_service'), ('ironing_board', 'ironing_board'),
               ('driver_quarters', 'driver_quarters'), ('wheel_chair', 'wheel_chair'),
               ('swimming_pool', 'swimming_pool'), ('gym', 'gym'), ('spa', 'spa'), ('guest_laundry', 'guest_laundry'),
               ('free_breakfast', 'free_breakfast'), ('card_payment_accepted', 'card_payment_accepted'), ('bar', 'bar'),
               ('restaurant', 'restaurant'),
               ('banquet_hall', 'banquet_hall')]

    form_extra_fields = {
        'amenity_name': SelectField('Amenity Name', choices=choices, default=1, validators=[validators.DataRequired()])
    }


class SkuCategoryAdmin(AdminAccessView):
    column_filters = ('id', 'name', 'code', 'hsn_sac', 'status')
    form_excluded_columns = ('code', 'created_at', 'modified_at')
    no_edit_fields = ('has_slab_based_taxation',)

    form_args = dict(
        hsn_sac=dict(validators=[DataRequired()]),
    )

    def on_model_change(self, form, model, is_created):
        try:
            if is_created:
                model.code = Utils.slugify(form.name.data)
        except CatalogingServiceException:
            logger.exception('Error in channel configuration')

    @action('bulk_category_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.sku_service.publish_categories(ids)


class ChannelAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name', 'description', 'status', 'priority')
    form_excluded_columns = ('created_at', 'modified_at', 'pricing_mapping', 'policies', 'sub_channels', 'applications')

    def on_model_change(self, form, model, is_created):
        try:
            if is_created:
                model.id = Utils.slugify(form.name.data)
        except CatalogingServiceException:
            logger.exception('Error in channel configuration')

    @action('bulk_channel_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.channel_service.publish_channels(ids)


class SubChannelAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name', 'description', 'status', 'channel_id', 'priority')
    form_excluded_columns = ('created_at', 'modified_at', 'pricing_mapping', 'policies')

    def on_model_change(self, form, model, is_created):
        try:
            if is_created:
                model.id = Utils.slugify(form.name.data)
        except CatalogingServiceException:
            logger.exception('Error in sub channel configuration')

    def after_model_change(self, form, model, is_created):
        if is_created:
            service_provider.channel_service.supdate_pricing_policy_for_sub_channel(model=model)
        super().after_model_change(form, model, is_created)

    @action('bulk_subchannel_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.channel_service.publish_sub_channels(ids)


class ApplicationAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'name', 'description', 'status', 'channel_id')

    def on_model_change(self, form, model, is_created):
        try:
            if is_created:
                model.id = Utils.slugify(form.name.data)
        except CatalogingServiceException:
            logger.exception('Error in application configuration')

    @action('bulk_application_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.channel_service.publish_applications(ids)


class ProviderAdmin(AdminAccessView):
    column_filters = ('id', 'name', 'code', 'status',)
    form_excluded_columns = ('created_at', 'modified_at', 'rate_plan', 'room_mappings')


class ProviderRoomMappingAdmin(AdminAccessView):
    column_filters = ('provider.name', 'room_type.type', 'ext_room_code', 'ext_room_name')


class SkuAdmin(AdminAccessView, NoDeleteView):
    page_size = 50
    column_filters = ('code', 'name', 'hsn_sac', 'saleable', 'category_id', 'is_property_inclusion', 'tax_at_room_rate')
    form_excluded_columns = ('created_at', 'modified_at',
                             'properties', 'property_sku', 'bundle', 'sku', 'code',)
    no_edit_fields = ('is_modular', 'sku_type', 'category', 'chargeable_per_occupant')
    column_list = ('category', 'tax_type', 'sku_type', 'bundle_rule.id', 'id', 'code', 'name', 'is_modular', 'hsn_sac',
                   'description', 'saleable', 'default_list_price', 'default_sale_price',
                   'chargeable_per_occupant', 'identifier', 'sku_count',
                   'display_name','is_default_sku_for_property_launch')
    form_create_rules = (
        'name', 'is_modular', 'tax_type', 'sku_type', 'category', 'chargeable_per_occupant', 'flat_count_for_creation',
        'default_list_price', 'default_sale_price', 'saleable', 'description', 'hsn_sac', 'sku_count', 'display_name',
        'offering', 'frequency', 'is_default_sku_for_property_launch')
    form_edit_rules = (
        'name', 'is_modular', 'tax_type', 'sku_type', 'category', 'chargeable_per_occupant', 'default_list_price',
        'default_sale_price', 'saleable', 'description', 'hsn_sac', 'sku_s', 'sku_count', 'display_name',
        'offering', 'frequency', 'is_property_inclusion', 'tax_at_room_rate', 'is_default_sku_for_property_launch')

    form_widget_args = {
        'sku_s': {
            'disabled': True
        },
        'tag': {
            'disabled': True
        }
    }

    def on_form_prefill(self, form, id):
        form.sku_count.render_kw = {'readonly': True}

    def on_model_change(self, form, model, is_created):
        try:
            if str.lower(form.category.data.code) == 'composite' and (form.is_modular.data or
                                                                      form.chargeable_per_occupant.data):
                raise Exception('Bundle should have composite as category and is_modular as False and should not be '
                                'charged per occupant')
            if str.lower(form.sku_type.data) == 'sku' and form.sku_count.data > 0:
                raise Exception('Modular sku has a default count as 0')

            if form.is_modular.data and is_created:
                model.identifier = service_provider.sku_service.create_unique_identifier_for_sku(form)

            if not form.is_modular.data:
                model.default_list_price = 0
                model.default_sale_price = 0
        except Exception as e:
            raise validators.ValidationError(e)

    @action('bulk_sku_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.sku_service.publish_sku(ids)

    @action('bulk_sku_sync', 'Bulk Sync', 'Are you sure you want to sync the data?')
    def bulk_sync(self, ids):
        try:
            service_provider.sku_service.bulk_sync_to_redis(ids)
        except Exception as e:
            flash(str(e), category='error')


class SkuBundleAdmin(AdminAccessView, NoEditView):
    column_filters = ('bundle.code', 'bundle.name')
    form_excluded_columns = ('created_at', 'modified_at',)

    form_args = dict(
        bundle=dict(query_factory=lambda: service_provider.sku_service.sget_newly_created_sku()),
        sku=dict(query_factory=lambda: db_engine.get_session().query(Sku).filter_by(is_modular=True))
    )

    def on_model_change(self, form, model, is_created):
        """
        The idea is that if the identifier clashes, model creation sku_bundle
        should not be allowed, don't put the logic after_model_change
        :param form:
        :param model:
        :param is_created:
        :return:
        """
        try:
            model.bundle.identifier = service_provider.sku_service.update_identifier_in_sku(form.bundle.data)
            model.bundle.tag = service_provider.sku_service.update_tag_for_parent_sku(form.bundle.data)
        except Exception as e:
            raise validators.ValidationError(e)


class PropertySkuAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('id', 'property.id', 'property.name', 'sku.name')
    no_edit_fields = ('property', 'sku')
    column_list = ['property', 'sku', 'status', 'saleable', 'activation_callbacks_list', 'description',
                   'extra_information', 'sell_separate']

    column_labels = dict(status='Config status')

    form_widget_args = {
        'status': {
            'disabled': True
        },
        'sell_separate': {
            'disabled': True,
            'readonly': True,
        }
    }

    def on_model_change(self, form, model, is_created):
        if is_created:
            model.status = StandardStatusChoices.INACTIVE
            model.sell_separate = True
        if form.saleable.data and model.status == 'INACTIVE':
            raise validators.ValidationError('Sku has to be active for selling')
        if form.saleable.data and form.sku.data.is_modular:
            raise validators.ValidationError('Modular Sku can\'t be sold')
        if form.saleable.data and form.property.data.status not in (PropertyChoices.STATUS_LIVE):
            raise validators.ValidationError("Sku cannot be sold if the property is not in LIVE state")

    @action('disable_sku', 'Make property sku unsaleable',
            'Make sku unsaleable for property?')
    def bulk_disable_sku_for_property(self, ids):
        try:
            service_provider.property_service.make_given_property_sku_unsaleable(ids)
        except Exception as e:
            logger.exception('Failed to update property sku {}'.format(e))
            flash(str(e), category='error')

    @action('enable_sku', 'Make property sku saleable',
            'Make sku saleable for property?')
    def bulk_enable_sku_for_property(self, ids):
        try:
            service_provider.property_service.make_given_property_sku_saleable(ids)
        except Exception as e:
            logger.exception('Failed to update property sku {}'.format(e))
            flash(str(e), category='error')

    @action('sync_inventory_from_its', 'Trigger its sync inventory')
    def sync_inventory_from_its(self, ids):
        try:
            if len(ids) > 1:
                raise ValueError('Select only one record. It will be synced for the whole property')
            property_sku = service_provider.property_service.sget_property_sku_by_ids(ids)[0]
            ITS_HOST = ServiceRegistryClient.get_its_service_url() + "/its"
            start_date = Utils.get_current_date_ist()
            its_url = "{i}/crs/property_inventory_sync?hotel_id={h}&start_date={s}&end_date={e}&cache=override"
            its_url = its_url.format(
                i=ITS_HOST, h=property_sku.property_id,
                s=start_date.isoformat(), e=(start_date + datetime.timedelta(400)).isoformat()
            )
            response = requests.get(its_url)
            response.raise_for_status()
            flash('Success syncing inventory', category='success')
        except HTTPError as e:
            flash(str(e.response.text), category='error')
        except Exception as e:
            logger.exception('Failed to sync inventory property sku {}'.format(e))
            flash(str(e), category='error')

    @action('bulk_property_sku_push', 'Bulk Push', 'Are you sure you want to push the data')
    def bulk_push(self, ids):
        service_provider.property_service.publish_property_skus(ids)


class ParamAdmin(AdminAccessView):
    column_filters = ('entity', 'field', 'validate')
    form_excluded_columns = ('param_sku_activation',)

    # https://flask-admin.readthedocs.io/en/latest/introduction/#modelview-configuration-attributes
    choices = [(entity, entity) for entity in service_provider.meta_service.sget_all_entities()]
    form_choices = {
        'entity': choices
    }

    # validate values selected in values for model
    def on_model_change(self, form, model, is_created):
        try:
            if form.entity.data == "Sku" and form.field.data == 'bundle_rule':
                service_provider.sku_service.validate_bundle_rule(form.value.data)
                if not service_provider.sku_service.scheck_if_bundle_rule_is_unique():
                    raise Exception("One rule to be default at a time")

        except Exception as e:
            raise validators.ValidationError(e)


class PropertiesSkuCategoriesAdmin(AdminAccessView, NoEditView):
    column_filters = ('sku_category.id', 'sku_category.name', 'property.id', 'property.name')
    column_list = ['id', 'sku_category', 'property']
    form_columns = ['sku_category', 'property']

    column_labels = {
        'sku_category': 'Sku Category',
        'property': 'Property'
    }


class SkuAttributeAdmin(AdminAccessView):
    column_filters = ('sku.name', 'key')


class SkuActivationAdmin(AdminAccessView):
    column_filters = ('property.id', 'sku.code', 'sku.name', 'param.value')


class PricingPolicyAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('code', 'name', 'status')
    form_excluded_columns = ('code', 'created_at', 'modified_at', 'pricing_mapping', 'channels', 'sub_channels')

    def on_model_change(self, form, model, is_created):
        try:
            if is_created:
                model.code = Utils.slugify(form.name.data)
            if not service_provider.channel_service.scheck_if_pricing_policy_is_unique():
                raise Exception("One policy to be default at a time")
        except Exception as e:
            logger.exception('Error in pricing policy configuration')
            raise validators.ValidationError(e)

    @action('bulk_pricing_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, ids):
        service_provider.channel_service.publish_pricing_policies(ids)


class PricingMappingAdmin(AdminAccessView):
    column_filters = ('channel.name', 'sub_channel.name', 'status')

    def on_model_change(self, form, model, is_created):
        if form.sub_channel.data.channel.name != form.channel.data.name:
            raise validators.ValidationError("Invalid channel subchannel selection")


class BrandAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('code', 'name', 'display_name', 'status', 'brand_code')
    form_excluded_columns = ('created_at', 'modified_at', 'code')

    pattern = re.compile("^[a-fA-F0-9]{6}$", re.DOTALL)

    def validate_color(self, color):

        if (len(color) != 6) or not self.pattern.match(color):
            raise validators.ValidationError('Color should have 6 digit hexadecimal value')

    def on_model_change(self, form, model, is_created):
        self.validate_color(model.color)
        try:
            if is_created:
                model.code = Utils.slugify(form.name.data)
            if form.brand_code.object_data != form.brand_code.data:
                current_brand_properties_mappings = (PropertyBrandMapping.query.join(Brand).
                                                     filter(Brand.code == model.code).all())
                if not current_brand_properties_mappings:
                    return
                update_cc_id_properties = [mapping.property for mapping in current_brand_properties_mappings]
                for property in update_cc_id_properties:
                    old_cost_center_id = property.cost_center_id
                    new_cost_center_id = Utils.generate_cost_center_id(property.id, form.brand_code.data,
                                                                       property.region,
                                                                       property.location.city.name)
                    property.cost_center_id = new_cost_center_id
                    Utils.send_cost_center_id_change_email(property.id, old_cost_center_id, new_cost_center_id)

        except CatalogingServiceException:
            logger.exception('Error in sub brand configuration')


class ProviderBrandMappingAdmin(AdminAccessView):
    column_filters = ('provider.name', 'brand.name', 'status')


class PropertyBrandMappingAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('property.name', 'brand.name', 'status')

    def on_model_change(self, form, model, is_created):
        current_brand_code = model.property.cost_center_id.split('-')[0] if model.property.cost_center_id else None
        new_brand_code = form.data['brand'].brand_code
        if not current_brand_code or (new_brand_code != current_brand_code):
            if not model.property.region:
                raise validators.ValidationError('Region is missing from this property')
            old_cost_center_id = model.property.cost_center_id
            new_cost_center_id = Utils.generate_cost_center_id(model.property.id, new_brand_code, model.property.region,
                                                                          model.property.location.city.name)
            model.property.cost_center_id = new_cost_center_id
            Utils.send_cost_center_id_change_email(model.property.id, old_cost_center_id, new_cost_center_id)
        if not model.property.property_detail or (
                model.brand not in model.property.property_detail.provider.brands):
            raise validators.ValidationError('The property\'s provider does not allow for this brand')


class RuptubLegalEntityDetailsAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('state_id', 'state.name')
    column_list = ('id', 'state_id', 'state.name', 'gstin', 'date_of_registration', 'address_line_1', 'address_line_2',
                   'address_city', 'address_pincode', 'legal_name')
    form_excluded_columns = 'legal_name'
    column_labels = {
        'state.name': 'State'
    }


class PropertyPolicyMapAdmin(AdminAccessView, NoDeleteView):
    column_filters = ('property_details.property_id', 'property_policy_id')
    column_list = ('id', 'property_details.property_id', 'property_details.property.name', 'property_policy_id')
    column_labels = {
        'property_details.property_id': 'Property id',
        'property_details.property.name': 'Property Name',
        'property_policy_id': 'Property Policy id'
    }


class PropertyPolicyAdmin(AdminAccessView, NoDeleteView):
    column_list = ('id', 'policy_type', 'title', 'description', 'display_in_need_to_know', 'display_in_policy')
    form_excluded_columns = 'id'


class GlobalPolicyAdmin(AdminAccessView, NoDeleteView):
    column_list = ('id', 'policy_type', 'title', 'description', 'display_in_need_to_know', 'display_in_policy',)
    form_excluded_columns = 'id'


class SellerCategoryAdmin(AdminAccessView, NoDeleteView):
    pass


class SellerAdmin(AdminAccessView, NoDeleteView):
    form_excluded_columns = ('timezone', 'base_currency_code')

    def on_model_change(self, form, model, is_created):
        if model.property:
            model.timezone = repo_provider.property_repository.get_property_timezone(
                model.property.id)
            model.base_currency_code = repo_provider.property_repository.get_property_base_currency(
                model.property.id)


class MenuCategoryAdmin(AdminAccessView, NoDeleteView):
    pass


class SellerSkuAdmin(AdminAccessView, NoDeleteView):
    pass


class AddonTableAdmin(AdminAccessView, NoDeleteView):
    column_list = ('id', 'code', 'name')
    form_excluded_columns = 'id'
    can_create = True
    can_edit = True
    form_create_rules = ('code', 'name')


class RatePlanAddonTableAdmin(AdminAccessView, NoDeleteView):
    column_list = ('rate_plan_id', 'addon_id')
    column_default_sort = 'rate_plan_id'
    can_create = True
    can_edit = True
    form_create_rules = ('rate_plan', 'addon')


class NewRatePlanConfigTableAdmin(AdminAccessView, NoDeleteView):
    column_list = ('property_id', 'rate_plan_id', 'room_type_id')
    column_default_sort = 'rate_plan_id'
    can_create = True
    can_edit = True
    form_create_rules = ('property', 'rate_plan', 'room_type')


class NewRatePlanTableAdmin(AdminAccessView, NoDeleteView):
    column_list = ('id', 'code', 'name')
    form_excluded_columns = 'id'
    can_create = True
    can_edit = True
    form_create_rules = ('code', 'name')


class RestaurantTableAdmin(AdminAccessView, NoDeleteView):
    column_default_sort = 'area_id'
    column_exclude_list = ('x_coordinate', 'y_coordinate', 'height', 'width')
    form_excluded_columns = 'id'
    can_create = True
    can_edit = True


class AvailableConfigAdmin(AdminAccessView):
    column_filters = ('name',)
    column_default_sort = 'name'
    inline_models = (AllowedConfigValueModel,)
    no_edit_fields = ('name',)
    column_list = ('name', 'value_type', 'allowed_config_values')
    form_columns = ('name', 'value_type', 'description')
    form_choices = {
        'value_type': [(value_type, value_type) for value_type in ConfigValueType.all()]
    }
    column_formatters = {
        'allowed_config_values': lambda v, c, m, p: ', '.join(
            allowed_value.value for allowed_value in m.allowed_config_values)
    }

    def on_model_change(self, form, model, is_created):
        if model.allowed_config_values and model.value_type not in {ConfigValueType.STRING.value,
                                                                    ConfigValueType.ARRAY.value}:
            raise validators.ValidationError("Restricted values are only allowed for String and Array type configs")


class TenantConfigAdmin(AdminAccessView):
    column_default_sort = 'config_name'
    column_filters = ('config_name', 'property.name', 'property_id')
    column_exclude_list = ('created_at', 'modified_at', 'tenant_config_id')

    # validate values selected in values for model
    def on_model_change(self, form, model, is_created):
        try:
            model.validate_config_value()
        except Exception as e:
            raise validators.ValidationError(
                "Incorrect format has given {error}".format(error=e.__str__()))

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        return super().update_model(form, model)



class UserDefinedEnumAdmin(AdminAccessView):
    column_default_sort = 'enum_id'
    column_filters = ('enum_name', 'property.name', 'property.id', 'role')
    inline_models = (EnumValuesModel,)
    column_list = ('enum_id', 'property_id', 'enum_name', 'label', 'enum_values', 'role')
    column_formatters = {
        'enum_values': lambda v, c, m, p: ', '.join(enum_value.value for enum_value in m.enum_values)
    }


class CurrencyConversionRateAdmin(AdminAccessView, NoDeleteView):
    column_default_sort = 'modified_at'
    column_exclude_list = ('created_at', 'modified_at', 'currency_conversion_rate_id')

    # validate values selected in values for model
    def on_model_change(self, form, model, is_created):
        try:
            model.validate_date()
        except Exception as e:
            raise validators.ValidationError(
                "{error}".format(error=e.__str__()))


class RoomRackRateAdmin(AdminAccessView):
    column_list = ('room_rack_rate_id', 'property', 'room_type', 'adult_count', 'rack_rate')
    column_filters = ('property.name', 'room_type.type')
    form_excluded_columns = ('room_rack_rate_id', 'created_at', 'modified_at')
    column_default_sort = None
    can_create = True
    can_edit = True

    @action('bulk_room_rack_rate_push', 'Bulk Push', 'Are you sure you want to bulk push the data?')
    def bulk_push(self, room_rack_rate_ids):
        service_provider.room_service.publish_room_rack_rates(room_rack_rate_ids)

    def on_model_change(self, form, model, is_created):
        if form.data['adult_count'] > 100:
            raise validators.ValidationError("Adult Count can't be greater than 100")


class KitchenAdminView(AdminAccessView):
    pass



class SellerSkuCategoryAdmin(AdminAccessView):
    column_filters = ('seller', 'sku_category')
    column_list = ('seller', 'sku_category')
    column_default_sort = None


def format_timestamp(view, context, model, name):
    ts = model.timestamp
    if not ts:
        return ""
    if ts.tzinfo is None:
        ts = UTC.localize(ts)
    local_ts = ts.astimezone(timezone("Asia/Kolkata"))

    return local_ts.strftime("%b %d, %Y, %I:%M %p")


class AuditLogAdmin(AdminAccessView):
    can_create = False
    can_edit = False
    can_delete = False
    can_view_details = True
    page_size = 50
    column_default_sort = ('timestamp', True)
    column_list = (
        'timestamp', 'user_action', 'entity_name', 'entity_id',
        'user_details', 'source', 'request_id', 'details'
    )
    column_filters = ('user_action', 'entity_name', 'source', 'timestamp')
    column_searchable_list = ('entity_name', 'entity_id', 'request_id')
    column_formatters = {
        'timestamp': format_timestamp,
        'user_details': lambda v, c, m, p: str(m.user_details),
        'details': lambda v, c, m, p: str(m.details)
    }


# ============================================================================
# Phase 2: Department/Profit Center Admin Views
# ============================================================================

class DepartmentTemplateAdmin(AdminAccessView):
    """Admin view for Department Templates"""
    column_filters = ('id', 'brand_id', 'code', 'name', 'parent_code', 'is_active')
    column_list = ('id', 'brand_id', 'code', 'name', 'financial_code', 'parent_code',
                   'auto_create_on_property_launch', 'is_active', 'created_at', 'modified_at')
    column_searchable_list = ('code', 'name', 'description')
    column_default_sort = ('brand_id', 'code')

    form_columns = ('brand_id', 'code', 'name', 'financial_code', 'parent_code',
                    'description', 'auto_create_on_property_launch', 'is_active')

    column_labels = {
        'brand_id': 'Brand ID',
        'code': 'Department Code',
        'name': 'Department Name',
        'financial_code': 'Financial/GL Code',
        'parent_code': 'Parent Department Code',
        'auto_create_on_property_launch': 'Auto Create on Launch',
        'is_active': 'Active'
    }

    form_args = {
        'code': {'validators': [DataRequired(), Regexp(r'^[A-Z0-9_-]+$', message="Code must contain only uppercase letters, numbers, hyphens, and underscores")]},
        'name': {'validators': [DataRequired()]},
        'brand_id': {'validators': [DataRequired()]}
    }


class ProfitCenterTemplateAdmin(AdminAccessView):
    """Admin view for Profit Center Templates"""
    column_filters = ('id', 'brand_id', 'code', 'name', 'department_template_code', 'is_active')
    column_list = ('id', 'brand_id', 'code', 'name', 'department_template_code',
                   'system_interface', 'auto_create_on_property_launch', 'is_active', 'created_at', 'modified_at')
    column_searchable_list = ('code', 'name', 'description')
    column_default_sort = ('brand_id', 'department_template_code', 'code')

    form_columns = ('brand_id', 'code', 'name', 'department_template_code',
                    'system_interface', 'description', 'auto_create_on_property_launch', 'is_active')

    column_labels = {
        'brand_id': 'Brand ID',
        'code': 'Profit Center Code',
        'name': 'Profit Center Name',
        'department_template_code': 'Department Template Code',
        'system_interface': 'System Interface',
        'auto_create_on_property_launch': 'Auto Create on Launch',
        'is_active': 'Active'
    }

    form_args = {
        'code': {'validators': [DataRequired(), Regexp(r'^[A-Z0-9_-]+$', message="Code must contain only uppercase letters, numbers, hyphens, and underscores")]},
        'name': {'validators': [DataRequired()]},
        'brand_id': {'validators': [DataRequired()]},
        'department_template_code': {'validators': [DataRequired()]}
    }


class DepartmentAdmin(AdminAccessView):
    """Admin view for Property Departments"""
    column_filters = ('id', 'property_id', 'code', 'name', 'template_code', 'is_custom', 'is_active')
    column_list = ('id', 'property_id', 'code', 'name', 'template_code', 'financial_code',
                   'parent_id', 'is_custom', 'is_active', 'created_at', 'modified_at')
    column_searchable_list = ('code', 'name', 'description', 'property_id')
    column_default_sort = ('property_id', 'code')

    form_columns = ('property_id', 'code', 'name', 'template_code', 'financial_code',
                    'parent_id', 'description', 'is_custom', 'is_active')

    column_labels = {
        'property_id': 'Property ID',
        'code': 'Department Code',
        'name': 'Department Name',
        'template_code': 'Template Code',
        'financial_code': 'Financial/GL Code',
        'parent_id': 'Parent Department ID',
        'is_custom': 'Custom Department',
        'is_active': 'Active'
    }

    form_args = {
        'code': {'validators': [DataRequired(), Regexp(r'^[A-Z0-9_-]+$', message="Code must contain only uppercase letters, numbers, hyphens, and underscores")]},
        'name': {'validators': [DataRequired()]},
        'property_id': {'validators': [DataRequired()]}
    }


class TransactionMasterAdmin(AdminAccessView):
    """Admin view for Transaction Master"""
    column_filters = ('id', 'transaction_code', 'property_id', 'entity_type', 'transaction_type', 'status')
    column_list = ('id', 'transaction_code', 'name', 'property_id', 'entity_type', 'transaction_type',
                   'gl_code', 'erp_id', 'status', 'is_merge', 'created_at', 'modified_at')
    column_searchable_list = ('transaction_code', 'name', 'property_id', 'transaction_id')
    column_default_sort = ('property_id', 'transaction_code')

    form_columns = ('transaction_code', 'name', 'property_id', 'entity_type', 'transaction_type',
                    'transaction_id', 'operational_unit_id', 'operational_unit_type', 'source',
                    'gl_code', 'erp_id', 'is_merge', 'particulars', 'status', 'transaction_details')

    column_labels = {
        'transaction_code': 'Transaction Code',
        'name': 'Transaction Name',
        'property_id': 'Property ID',
        'entity_type': 'Entity Type',
        'transaction_type': 'Transaction Type',
        'transaction_id': 'Transaction ID',
        'operational_unit_id': 'Operational Unit ID',
        'operational_unit_type': 'Operational Unit Type',
        'source': 'Source System',
        'gl_code': 'GL Code',
        'erp_id': 'ERP ID',
        'is_merge': 'Merge Flag',
        'particulars': 'Particulars',
        'status': 'Status',
        'transaction_details': 'Transaction Details (JSON)'
    }

    form_args = {
        'transaction_code': {'validators': [DataRequired()]},
        'name': {'validators': [DataRequired()]},
        'property_id': {'validators': [DataRequired()]},
        'entity_type': {'validators': [DataRequired()]},
        'transaction_type': {'validators': [DataRequired()]},
        'transaction_id': {'validators': [DataRequired()]},
        'operational_unit_id': {'validators': [DataRequired()]},
        'operational_unit_type': {'validators': [DataRequired()]},
        'source': {'validators': [DataRequired()]}
    }

    def _transaction_details_formatter(self, context, model, name):
        """Format the transaction_details JSON field for display"""
        try:
            import json
            if model.transaction_details:
                formatted_json = json.dumps(model.transaction_details, indent=2, ensure_ascii=False)
                return Markup(f'<pre style="max-height: 200px; overflow-y: auto; white-space: pre-wrap;">{formatted_json}</pre>')
            return ""
        except Exception:
            return str(model.transaction_details) if model.transaction_details else ""

    column_formatters = {
        'transaction_details': _transaction_details_formatter
    }


class TransactionDefaultMappingAdmin(AdminAccessView):
    """Admin view for Transaction Default Mappings"""
    column_filters = ('id', 'brand_id', 'transaction_type', 'transaction_type_code', 'entity_type', 'is_active')
    column_list = ('id', 'brand_id', 'transaction_type', 'transaction_type_code', 'entity_type',
                   'default_gl_code', 'default_erp_id', 'default_is_merge', 'is_active', 'created_at', 'modified_at')
    column_searchable_list = ('transaction_type', 'transaction_type_code', 'entity_type', 'default_particulars')
    column_default_sort = ('brand_id', 'transaction_type', 'transaction_type_code')

    form_columns = ('brand_id', 'transaction_type', 'transaction_type_code', 'entity_type',
                    'default_gl_code', 'default_erp_id', 'default_particulars', 'default_is_merge',
                    'transaction_details', 'is_active')

    column_labels = {
        'brand_id': 'Brand ID',
        'transaction_type': 'Transaction Type',
        'transaction_type_code': 'Transaction Type Code',
        'entity_type': 'Entity Type',
        'default_gl_code': 'Default GL Code',
        'default_erp_id': 'Default ERP ID',
        'default_particulars': 'Default Particulars',
        'default_is_merge': 'Default Merge Flag',
        'transaction_details': 'Transaction Details (JSON)',
        'is_active': 'Active'
    }

    form_args = {
        'brand_id': {'validators': [DataRequired()]},
        'transaction_type': {'validators': [DataRequired()]},
        'transaction_type_code': {'validators': [DataRequired()]},
        'entity_type': {'validators': [DataRequired()]}
    }

    def _transaction_details_formatter(self, context, model, name):
        """Format the transaction_details JSON field for display"""
        try:
            import json
            if model.transaction_details:
                formatted_json = json.dumps(model.transaction_details, indent=2, ensure_ascii=False)
                return Markup(f'<pre style="max-height: 200px; overflow-y: auto; white-space: pre-wrap;">{formatted_json}</pre>')
            return ""
        except Exception:
            return str(model.transaction_details) if model.transaction_details else ""

    column_formatters = {
        'transaction_details': _transaction_details_formatter
    }


def get_admin_views(tenant_id=None):
    session = db_engine.get_scoped_session(tenant_id=tenant_id)
    views = [
        SystemPropertyAdmin(tenant_id, SystemProperty, session, category='System Admin'),
        UserAdmin(tenant_id, User, session, category='System Admin'),
        RoleAdmin(tenant_id, Role, session, category='System Admin'),
        ParamAdmin(tenant_id, Param, session, category='System Admin'),
        PropertyAdmin(tenant_id, Property, session, category='Property Admin'),
        BankDetailAdmin(tenant_id, BankDetail, session, category='Property Admin'),
        PropertyDetailAdmin(tenant_id, PropertyDetail, session, category='Property Admin'),
        PropertyPolicyAdmin(tenant_id, PropertyPolicy, session, category='Property Policy'),
        GlobalPolicyAdmin(tenant_id, GlobalPolicy, session, category='Global Policy'),
        PropertyPolicyMapAdmin(tenant_id, PropertyPolicyMap, session, category='Property Policy'),
        GoogleDriveBaseFolderAdmin(tenant_id, GoogleDriveBaseFolder, session, category='File Upload'),
        GoogleDriveFileUploadAdmin(tenant_id, GoogleDriveFile, session, category='File Upload'),
        CountryAdmin(tenant_id, Country, session, category='Location Admin'),
        RegionAdmin(tenant_id, Region, session, category='Location Admin'),
        ClusterAdmin(tenant_id, Cluster, session, category='Location Admin'),
        StateAdmin(tenant_id, State, session, category='Location Admin'),
        CityAdmin(tenant_id, City, session, category='Location Admin'),
        MicroMarketAdmin(tenant_id, MicroMarket, session, category='Location Admin'),
        LocalityAdmin(tenant_id, Locality, session, category='Location Admin'),
        LocationAdmin(tenant_id, Location, session, category='Location Admin'),
        RoomTypeAdmin(tenant_id, RoomType, session, category='Room Admin'),
        RoomAdmin(tenant_id, Room, session, category='Room Admin'),
        RoomTypeConfigurationAdmin(tenant_id, RoomTypeConfiguration, session, category='Room Admin'),
        GuestFacingAdmin(tenant_id, GuestFacingProcess, session, category='Property Admin'),
        GuestTypeAdmin(tenant_id, GuestType, session, category='Property Admin'),
        GuestTypePropertyAdmin(tenant_id, GuestTypeProperty, session, category='Property Admin'),
        OwnerAdmin(tenant_id, Owner, session, category='Property Admin'),
        CommonAmenityAdmin(tenant_id, AmenityPublicWashroom, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityElevator, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityParking, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityDisableFriendly, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenitySwimmingPool, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityGym, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityPrivateCab, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenitySpa, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityLaundry, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityBreakfast, session, category='Amenity Admin'),
        CommonAmenityAdmin(tenant_id, AmenityPayment, session, category='Amenity Admin'),
        CuisineAdmin(tenant_id, Cuisine, session, category='Amenity Admin'),
        PropertyAmenityAdmin(tenant_id, PropertyAmenity, session, category='Amenity Admin'),
        BarAdmin(tenant_id, Bar, session, category='Amenity Admin'),
        RestaurantAdmin(tenant_id, Restaurant, session, category='Amenity Admin'),
        BanquetHallAdmin(tenant_id, BanquetHall, session, category='Amenity Admin'),
        RoomAmenityCommonAdmin(tenant_id, AmenityIntercom, session, category='Amenity Admin'),
        RoomAmenityCommonAdmin(tenant_id, AmenityHotWater, session, category='Amenity Admin'),
        RoomAmenityCommonAdmin(tenant_id, AmenityTV, session, category='Amenity Admin'),
        RoomAmenityCommonAdmin(tenant_id, AmenityAC, session, category='Amenity Admin'),
        RoomAmenityCommonAdmin(tenant_id, AmenityHeater, session, category='Amenity Admin'),
        RoomAmenityCommonAdmin(tenant_id, AmenityStove, session, category='Amenity Admin'),
        RoomAmenityCommonAdmin(tenant_id, AmenityTwinBed, session, category='Amenity Admin'),
        RoomAmenityAdmin(tenant_id, RoomAmenity, session, category='Amenity Admin'),
        LandmarkAdmin(tenant_id, Landmark, session, category='Property Admin'),
        DescriptionAdmin(tenant_id, Description, session, category='Property Admin'),
        NeighbouringPlaceAdmin(tenant_id, NeighbouringPlace, session, category='Property Admin'),
        OwnershipAdmin(tenant_id, Ownership, session, category='Property Admin'),
        TransportStationAdmin(tenant_id, TransportStation, session, category='Property Admin'),
        TransportStationPropertyAdmin(tenant_id, TransportStationProperty, session, category='Property Admin'),
        NotificationAdmin(tenant_id, Notification, session, category='System Admin'),
        MigrationAdmin(tenant_id, MigrationDetail, session, category='System Admin'),
        PropertyImageAdmin(tenant_id, PropertyImage, session, category='File Upload'),
        PropertyVideoAdmin(tenant_id, PropertyVideo, session, category='File Upload'),
        CityAliasAdmin(tenant_id, CityAlias, session, category='Location Admin'),
        OtaPropertyAdmin(tenant_id, OtaProperty, session, category='OTA'),
        OtaAdmin(tenant_id, OTA, session, category='OTA'),
        RatePlanConfigurationAdmin(tenant_id, RatePlanConfiguration, session, category='OTA'),
        RatePlanAdmin(tenant_id, RatePlan, session, category='OTA'),
        AmenitySummaryAdmin(tenant_id, AmenitySummary, session, category='Amenity Admin'),
        FacilityCategoryAdmin(tenant_id, FacilityCategory, session, category='Amenity Admin'),
        FacilityCategoryMappingAdmin(tenant_id, FacilityCategoryMapping, session, category='Amenity Admin'),
        SkuCategoryAdmin(tenant_id, SkuCategory, session, category='Sku Admin'),
        PropertyLandmarkAdmin(tenant_id, PropertyLandmark, session, category='Property Admin'),
        ChannelAdmin(tenant_id, Channel, session, category='Channel Admin'),
        SubChannelAdmin(tenant_id, SubChannel, session, category='Channel Admin'),
        ApplicationAdmin(tenant_id, Application, session, category='Channel Admin'),
        ProviderAdmin(tenant_id, Provider, session, category='Manage Providers'),
        ProviderRoomMappingAdmin(tenant_id, ProviderRoomTypeMapping, session, category='Manage Providers'),
        SkuAdmin(tenant_id, Sku, session, category='Sku Admin'),
        SkuBundleAdmin(tenant_id, SkuBundle, session, category='Sku Admin'),
        SkuAttributeAdmin(tenant_id, SkuAttribute, session, category='Sku Admin'),
        PropertySkuAdmin(tenant_id, PropertySku, session, category='Property Admin'),
        PropertiesSkuCategoriesAdmin(tenant_id, PropertiesSkuCategories, session, category='Property Admin'),
        SkuActivationAdmin(tenant_id, SkuActivation, session, category='Property Admin'),
        PricingPolicyAdmin(tenant_id, PricingPolicy, session, category='Pricing Policy'),
        PricingMappingAdmin(tenant_id, PricingMapping, session, category='Pricing Policy'),
        BrandAdmin(tenant_id, Brand, session, category='Manage Brands'),
        ProviderBrandMappingAdmin(tenant_id, ProviderBrandMapping, session, category='Manage Brands'),
        PropertyBrandMappingAdmin(tenant_id, PropertyBrandMapping, session, category='Manage Brands'),
        RuptubLegalEntityDetailsAdmin(tenant_id, RuptubLegalEntityDetails, session, category='Ruptub Legal Entity'),
        SellerCategoryAdmin(tenant_id, SellerCategory, session, category='POS'),
        SellerAdmin(tenant_id, Seller, session, category='POS'),
        MenuCategoryAdmin(tenant_id, MenuCategory, session, category='POS'),
        SellerSkuAdmin(tenant_id, SellerSku, session, category='POS'),
        RestaurantTableAdmin(tenant_id, RestaurantTable, session, category='POS'),
        AddonTableAdmin(tenant_id, Addon, session, category='New Rate Plan'),
        RatePlanAddonTableAdmin(tenant_id, RatePlanAddon, session, category='New Rate Plan'),
        NewRatePlanConfigTableAdmin(tenant_id, NewRatePlanConfig, session, category='New Rate Plan'),
        NewRatePlanTableAdmin(tenant_id, NewRatePlan, session, category='New Rate Plan'),
        AvailableConfigAdmin(tenant_id, AvailableConfigModel, session, category='Tenant Config'),
        TenantConfigAdmin(tenant_id, TenantConfigModel, session, category='Tenant Config'),
        CurrencyConversionRateAdmin(tenant_id, CurrencyConversionRateModel, session, category='Currency Conversion'),
        UserDefinedEnumAdmin(tenant_id, UserDefinedEnumModel, session, category='Tenant Config'),
        RoomRackRateAdmin(tenant_id, RoomRackRateModel, session, category='Room Admin'),
        KitchenAdminView(tenant_id, Kitchen, session, category='POS'),
        SellerSkuCategoryAdmin(tenant_id, SellerSkuCategory, session, category='POS'),
        # Phase 2: Department/Profit Center Admin Views
        DepartmentTemplateAdmin(tenant_id, DepartmentTemplate, session, category='Department Management'),
        ProfitCenterTemplateAdmin(tenant_id, ProfitCenterTemplate, session, category='Department Management'),
        DepartmentAdmin(tenant_id, Department, session, category='Department Management'),
        # Transaction Management Admin Views
        TransactionMasterAdmin(tenant_id, TransactionMaster, session, category='Transaction Management'),
        TransactionDefaultMappingAdmin(tenant_id, TransactionDefaultMapping, session, category='Transaction Management'),
        AuditLogAdmin(tenant_id, AuditLogModel, session, name='Audit Trail', category='System Activity')    ,
    ]
    return views
