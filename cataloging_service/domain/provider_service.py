import logging

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException

logger = logging.getLogger(__name__)


class ProviderService:
    def __init__(self, provider_repository):
        self.__provider_repository = provider_repository

    def sget_provider(self, code):
        if not code:
            raise CatalogingServiceException(context='Empty provider code')

        provider = self.__provider_repository.rget_provider_by_code(code)

        if not provider:
            raise CatalogingServiceException(error_codes.PROVIDER_NOT_FOUND, context='provider code: %s' % code)

        return provider

    def sget_all_providers(self, codes=None):
        try:

            return self.__provider_repository.rget_all_providers(codes)
        except Exception as e:
            logger.error(e)
            raise CatalogingServiceException(error_codes.INTERNAL_SERVER_ERROR)

    def sget_all_ext_room_configs_property(self, provider=None):
        """
        # get distinct room configs by external codes and providers
        :param provider:
        :return:
        """
        room_config_mappings = self.__provider_repository.rget_all_ext_room_configs_property(provider)
        mappings = []
        for room_config_mapping in room_config_mappings:
            treebo_type = room_config_mapping.room_type.type if room_config_mapping.room_type else None
            treebo_code = room_config_mapping.room_type.code if room_config_mapping.room_type else None
            mappings.append(dict(treebo_type=treebo_type,
                                 treebo_code=treebo_code,
                                 ext_room_name=room_config_mapping.ext_room_name,
                                 ext_room_code=room_config_mapping.ext_room_code,
                                 ext_rate_name=room_config_mapping.ext_rate_plan_name,
                                 ext_rate_code=room_config_mapping.ext_rate_plan_code
                                 ))
        return mappings

    def sget_all_properties_by_provider(self, prov_code, codes=None):
        from cataloging_service.domain import service_provider
        provider = self.sget_provider(prov_code)
        try:
            return service_provider.property_service.get_properties_by_provider_and_photel_codes(provider, codes)
        except Exception as e:
            logger.error(e)
            raise CatalogingServiceException(error_codes.INTERNAL_SERVER_ERROR)
