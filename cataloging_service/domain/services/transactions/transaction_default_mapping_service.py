from typing import List, Optional
from object_registry import register_instance
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.infrastructure.repositories.transaction_default_mapping_repository import TransactionDefaultMappingRepository


@register_instance(dependencies=[TransactionDefaultMappingRepository])
class TransactionDefaultMappingService:
    """Service for transaction default mapping business logic"""

    def __init__(self, repository: TransactionDefaultMappingRepository):
        self.repository: TransactionDefaultMappingRepository = repository

    def create_default_mapping(self, entity: TransactionDefaultMappingEntity) -> TransactionDefaultMappingEntity:
        """Create a new transaction default mapping with business validation"""
        # Business rule: Check for duplicate mapping within brand
        if self.repository.exists_by_criteria(
            entity.brand_id, 
            entity.transaction_type, 
            entity.transaction_type_code, 
            entity.entity_type
        ):
            raise ValueError(
                f"Transaction default mapping already exists for brand {entity.brand_id}, "
                f"type {entity.transaction_type}, code {entity.transaction_type_code}, entity {entity.entity_type}"
            )

        return self.repository.create(entity)

    def update_default_mapping(self, entity: TransactionDefaultMappingEntity) -> TransactionDefaultMappingEntity:
        """Update transaction default mapping with business validation"""
        # Business rule: Check for duplicate mapping within brand (excluding current)
        if self.repository.exists_by_criteria(
            entity.brand_id, 
            entity.transaction_type, 
            entity.transaction_type_code, 
            entity.entity_type,
            exclude_id=entity.id
        ):
            raise ValueError(
                f"Transaction default mapping already exists for brand {entity.brand_id}, "
                f"type {entity.transaction_type}, code {entity.transaction_type_code}, entity {entity.entity_type}"
            )

        return self.repository.update(entity)

    def get_default_mapping_by_id(self, mapping_id: int) -> Optional[TransactionDefaultMappingEntity]:
        """Get transaction default mapping by ID"""
        return self.repository.get_by_id(mapping_id)

    def get_default_mappings_by_brand(self, brand_id: int, active_only: bool = True) -> List[TransactionDefaultMappingEntity]:
        """Get transaction default mappings for a brand"""
        return self.repository.get_by_brand(brand_id, active_only)

    def get_mapping_for_transaction(self, brand_id: int, transaction_type: str, transaction_type_code: str, entity_type: str) -> Optional[TransactionDefaultMappingEntity]:
        """Get the default mapping for a specific transaction type"""
        return self.repository.get_by_transaction_type(brand_id, transaction_type, transaction_type_code, entity_type)

    def get_mappings_by_type(self, brand_id: int, transaction_type: str, entity_type: str) -> List[TransactionDefaultMappingEntity]:
        """Get all mappings for a transaction type and entity type"""
        return self.repository.get_by_transaction_type_pattern(brand_id, transaction_type, entity_type)

    def delete_default_mapping(self, mapping_id: int) -> bool:
        """Delete transaction default mapping"""
        return self.repository.delete(mapping_id)

    def get_available_transaction_types(self, brand_id: int) -> List[str]:
        """Get all available transaction types for a brand"""
        return self.repository.get_all_transaction_types(brand_id)

    def get_available_entity_types(self, brand_id: int, transaction_type: Optional[str] = None) -> List[str]:
        """Get all available entity types for a brand"""
        return self.repository.get_all_entity_types(brand_id, transaction_type)

    def bulk_create_default_mappings(self, mappings: List[TransactionDefaultMappingEntity]) -> List[TransactionDefaultMappingEntity]:
        """Create multiple default mappings with validation"""
        created_mappings = []
        
        for mapping in mappings:
            try:
                created_mapping = self.create_default_mapping(mapping)
                created_mappings.append(created_mapping)
            except ValueError as e:
                # Log the error but continue with other mappings
                # In a real implementation, you might want to collect errors and return them
                print(f"Failed to create mapping: {e}")
                continue
        
        return created_mappings
