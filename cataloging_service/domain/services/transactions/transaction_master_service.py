from typing import Optional, Dict, Any, List
from object_registry import register_instance
from cataloging_service.domain.entities.transactions.transaction_master import TransactionMasterEntity
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.infrastructure.repositories.transaction_master_repository import TransactionMasterRepository
from cataloging_service.infrastructure.repositories.transaction_default_mapping_repository import TransactionDefaultMappingRepository


@register_instance(dependencies=[TransactionMasterRepository, TransactionDefaultMappingRepository])
class TransactionMasterService:
    """Enhanced transaction master service with department support"""

    def __init__(self, transaction_master_repository: TransactionMasterRepository, transaction_default_mapping_repository: TransactionDefaultMappingRepository):
        self.transaction_master_repository = transaction_master_repository
        self.transaction_default_mapping_repository = transaction_default_mapping_repository

    def create_transaction_with_department_mapping(
        self,
        entity: TransactionMasterEntity,
        brand_id: int,
        department_code: Optional[str] = None,
        profit_center_code: Optional[str] = None
    ) -> TransactionMasterEntity:
        """Create transaction with department/profit center mapping"""
        
        # Business rule: Check for duplicate transaction code
        if self.transaction_master_repository.exists_by_transaction_code(entity.transaction_code):
            raise ValueError(f"Transaction with code '{entity.transaction_code}' already exists")

        # Get default mapping for transaction type
        default_mapping = self.transaction_default_mapping_repository.get_by_transaction_type(
            brand_id, entity.transaction_type, entity.transaction_type, entity.entity_type
        )

        if default_mapping:
            # Apply department-specific mapping if department is provided
            if department_code and profit_center_code:
                dept_mapping = default_mapping.get_profit_center_specific_mapping(department_code, profit_center_code)
            elif department_code:
                dept_mapping = default_mapping.get_department_specific_mapping(department_code)
            else:
                # Use default mapping without department context
                dept_mapping = {
                    "gl_code": default_mapping.default_gl_code,
                    "erp_id": default_mapping.default_erp_id,
                    "particulars": default_mapping.default_particulars,
                    "is_merge": default_mapping.default_is_merge,
                    "transaction_details": default_mapping.transaction_details
                }

            # Apply mapping to entity
            if dept_mapping.get("gl_code") and not entity.gl_code:
                entity.gl_code = dept_mapping["gl_code"]
            if dept_mapping.get("erp_id") and not entity.erp_id:
                entity.erp_id = dept_mapping["erp_id"]
            if dept_mapping.get("particulars") and not entity.particulars:
                entity.particulars = dept_mapping["particulars"]
            
            entity.is_merge = dept_mapping.get("is_merge", entity.is_merge)
            
            # Merge transaction details
            entity.transaction_details.update(dept_mapping.get("transaction_details", {}))

        # Add department context if provided
        if department_code:
            entity.add_department_context(department_code, profit_center_code)

        return self.transaction_master_repository.create(entity)

    def create_transaction(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        """Create transaction (backward compatibility)"""
        # Business rule: Check for duplicate transaction code
        if self.transaction_master_repository.exists_by_transaction_code(entity.transaction_code):
            raise ValueError(f"Transaction with code '{entity.transaction_code}' already exists")
            
        return self.transaction_master_repository.create(entity)

    def update_transaction(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        """Update transaction with business validation"""
        # Business rule: Check for duplicate transaction code (excluding current)
        if self.transaction_master_repository.exists_by_transaction_code(entity.transaction_code, exclude_id=entity.id):
            raise ValueError(f"Transaction with code '{entity.transaction_code}' already exists")
            
        return self.transaction_master_repository.update(entity)

    def get_transaction_by_id(self, transaction_id: int) -> Optional[TransactionMasterEntity]:
        """Get transaction by ID"""
        return self.transaction_master_repository.get_by_id(transaction_id)

    def get_transaction_by_code(self, transaction_code: str) -> Optional[TransactionMasterEntity]:
        """Get transaction by transaction code"""
        return self.transaction_master_repository.get_by_transaction_code(transaction_code)

    def get_transactions_by_property(self, property_id: str, status: Optional[str] = None) -> List[TransactionMasterEntity]:
        """Get transactions for a property"""
        return self.transaction_master_repository.get_by_property(property_id, status)

    def get_transactions_by_type(self, transaction_type: str, entity_type: str, property_id: Optional[str] = None) -> List[TransactionMasterEntity]:
        """Get transactions by type"""
        return self.transaction_master_repository.get_by_transaction_type(transaction_type, entity_type, property_id)

    def get_department_transactions(self, property_id: str, department_code: str) -> List[TransactionMasterEntity]:
        """Get transactions for a specific department"""
        return self.transaction_master_repository.get_department_transactions(property_id, department_code)

    def update_transaction_department(
        self,
        transaction_id: int,
        department_code: str,
        profit_center_code: Optional[str] = None
    ) -> TransactionMasterEntity:
        """Update transaction with department/profit center information"""
        transaction = self.transaction_master_repository.get_by_id(transaction_id)
        if not transaction:
            raise ValueError(f"Transaction {transaction_id} not found")

        # Update transaction details with department information
        transaction.add_department_context(department_code, profit_center_code)

        return self.transaction_master_repository.update(transaction)

    def delete_transaction(self, transaction_id: int) -> bool:
        """Delete transaction"""
        return self.transaction_master_repository.delete(transaction_id)

    def get_transactions_by_operational_unit(self, operational_unit_id: str, operational_unit_type: str) -> List[TransactionMasterEntity]:
        """Get transactions by operational unit"""
        return self.transaction_master_repository.get_by_operational_unit(operational_unit_id, operational_unit_type)
