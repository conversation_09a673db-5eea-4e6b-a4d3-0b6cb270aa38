from typing import List, Optional
from object_registry import register_instance
from cataloging_service.domain.entities.properties.property_department import PropertyDepartmentEntity
from cataloging_service.infrastructure.repositories.property_department_repository import PropertyDepartmentRepository


@register_instance(dependencies=[PropertyDepartmentRepository])
class PropertyDepartmentService:
    """Service for property department business logic"""

    def __init__(self, repository: PropertyDepartmentRepository):
        self.repository: PropertyDepartmentRepository = repository

    def create_department(self, entity: PropertyDepartmentEntity) -> PropertyDepartmentEntity:
        """Create a new property department with business validation"""
        # Business rule: Check for duplicate code within property
        if self.repository.exists_by_property_and_code(entity.property_id, entity.code):
            raise ValueError(
                f"Department with code '{entity.code}' already exists for property {entity.property_id}"
            )

        return self.repository.create(entity)

    def update_department(self, entity: PropertyDepartmentEntity) -> PropertyDepartmentEntity:
        """Update property department with business validation"""
        # Business rule: Check for duplicate code within property (excluding current)
        if self.repository.exists_by_property_and_code(entity.property_id, entity.code, exclude_id=entity.id):
            raise ValueError(
                f"Department with code '{entity.code}' already exists for property {entity.property_id}"
            )

        return self.repository.update(entity)

    def get_department_by_id(self, department_id: int) -> Optional[PropertyDepartmentEntity]:
        """Get department by ID"""
        return self.repository.get_by_id(department_id)

    def get_departments_by_property(self, property_id: str, active_only: bool = True) -> List[PropertyDepartmentEntity]:
        """Get departments for a property"""
        return self.repository.get_by_property(property_id, active_only)

    def get_root_departments(self, property_id: str) -> List[PropertyDepartmentEntity]:
        """Get root departments for a property"""
        return self.repository.get_root_departments(property_id)

    def delete_department(self, department_id: int) -> bool:
        """Delete property department"""
        return self.repository.delete(department_id)
