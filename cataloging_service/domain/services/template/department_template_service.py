from typing import List, Optional
from object_registry import register_instance
from cataloging_service.domain.entities.templates.department_template import DepartmentTemplateEntity
from cataloging_service.infrastructure.repositories.department_template_repository import DepartmentTemplateRepository


@register_instance(dependencies=[DepartmentTemplateRepository])
class DepartmentTemplateService:
    """Service for department template business logic"""

    def __init__(self, repository: DepartmentTemplateRepository):
        self.repository: DepartmentTemplateRepository = repository

    def create_department_template(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Create a new department template with business validation"""
        # Business rule: Check for duplicate code within brand
        if self.repository.exists_by_brand_and_code(entity.brand_id, entity.code):
            raise ValueError(
                f"Department template with code '{entity.code}' already exists for brand {entity.brand_id}"
            )

        # Business rule: Validate hierarchy if parent is specified
        if entity.parent_code:
            existing_templates = self.repository.get_by_brand(entity.brand_id, active_only=False)
            if not entity.validate_hierarchy(existing_templates):
                raise ValueError(
                    f"Invalid hierarchy: Parent department '{entity.parent_code}' not found or circular reference detected"
                )

        return self.repository.create(entity)

    def update_department_template(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Update department template with business validation"""
        # Business rule: Check for duplicate code within brand (excluding current)
        if self.repository.exists_by_brand_and_code(entity.brand_id, entity.code, exclude_id=entity.id):
            raise ValueError(
                f"Department template with code '{entity.code}' already exists for brand {entity.brand_id}"
            )

        # Business rule: Validate hierarchy if parent is specified
        if entity.parent_code:
            existing_templates = self.repository.get_by_brand(entity.brand_id, active_only=False)
            if not entity.validate_hierarchy(existing_templates):
                raise ValueError(
                    f"Invalid hierarchy: Parent department '{entity.parent_code}' not found or circular reference detected"
                )

        return self.repository.update(entity)

    def get_department_template_by_id(self, template_id: int) -> Optional[DepartmentTemplateEntity]:
        """Get department template by ID"""
        return self.repository.get_by_id(template_id)

    def get_department_templates_by_brand(self, brand_id: int, active_only: bool = True) -> List[DepartmentTemplateEntity]:
        """Get department templates for a brand"""
        return self.repository.get_by_brand(brand_id, active_only)

    def get_auto_create_templates(self, brand_id: int) -> List[DepartmentTemplateEntity]:
        """Get templates that should be auto-created on property launch"""
        return self.repository.get_auto_create_templates(brand_id)

    def delete_department_template(self, template_id: int) -> bool:
        """Delete department template with business validation"""
        # Business rule: Check if template is referenced by profit center templates
        # This would require checking profit center templates - simplified for now
        return self.repository.delete(template_id)
