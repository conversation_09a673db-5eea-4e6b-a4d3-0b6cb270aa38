from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic.types import PositiveInt
from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class ProfitCenterTemplateEntity(BaseDomainEntity):
    """Brand-scoped profit center template domain entity"""

    id: Optional[PositiveInt] = None
    brand_id: PositiveInt = Field(..., description="Brand ID this template belongs to")
    code: str = Field(..., min_length=2, max_length=50, description="Unique profit center code")
    name: str = Field(..., min_length=1, max_length=255, description="Profit center name")
    department_template_code: str = Field(..., description="Associated department template code")
    system_interface: Optional[str] = Field(None, max_length=100, description="System interface type")
    description: Optional[str] = Field(None, max_length=1000, description="Profit center description")
    auto_create_on_property_launch: bool = Field(False, description="Auto-create on property launch")
    is_active: bool = Field(True, description="Whether template is active")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("code")
    @classmethod
    def validate_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Code must contain only alphanumeric characters, hyphens, and underscores")
        return v.upper()
