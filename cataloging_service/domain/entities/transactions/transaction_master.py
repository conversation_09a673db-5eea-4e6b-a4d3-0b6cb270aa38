from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic.types import PositiveInt
from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class TransactionMasterEntity(BaseDomainEntity):
    """Transaction master domain entity for financial tracking"""

    id: Optional[PositiveInt] = None
    transaction_code: str = Field(..., min_length=1, max_length=100, description="Unique transaction code")
    name: str = Field(..., min_length=1, max_length=255, description="Transaction name")
    property_id: str = Field(..., description="Property ID this transaction belongs to")
    entity_type: str = Field(..., min_length=1, max_length=50, description="Entity type or scope")
    transaction_type: str = Field(..., min_length=1, max_length=50, description="Transaction type")
    transaction_id: str = Field(..., min_length=1, max_length=100, description="Transaction identifier")
    operational_unit_id: str = Field(..., min_length=1, max_length=100, description="Operational unit ID")
    operational_unit_type: str = Field(..., min_length=1, max_length=50, description="Operational unit type")
    source: str = Field(..., min_length=1, max_length=100, description="Transaction source")
    gl_code: Optional[str] = Field(None, max_length=50, description="General Ledger code")
    erp_id: Optional[str] = Field(None, max_length=100, description="ERP system ID")
    is_merge: bool = Field(False, description="Whether transaction should be merged")
    particulars: Optional[str] = Field(None, description="Transaction particulars/description")
    status: str = Field("ACTIVE", description="Transaction status")
    transaction_details: Dict[str, Any] = Field(default_factory=dict, description="Additional transaction details")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("transaction_code")
    @classmethod
    def validate_transaction_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Transaction code must contain only alphanumeric characters, hyphens, and underscores")
        return v.upper()

    @field_validator("status")
    @classmethod
    def validate_status(cls, v: str) -> str:
        allowed_statuses = ["ACTIVE", "INACTIVE", "PENDING", "COMPLETED", "CANCELLED"]
        if v.upper() not in allowed_statuses:
            raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v.upper()

    def add_department_context(self, department_code: str, profit_center_code: Optional[str] = None) -> None:
        """Add department and profit center context to transaction details"""
        self.transaction_details["department_code"] = department_code
        if profit_center_code:
            self.transaction_details["profit_center_code"] = profit_center_code

    def is_department_specific(self) -> bool:
        """Check if transaction has department-specific context"""
        return "department_code" in self.transaction_details
