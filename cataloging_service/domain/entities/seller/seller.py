from cataloging_service.domain.entities.base_entity import BaseDomainEntity
from pydantic import PositiveInt
from datetime import datetime, date

from typing import Optional

class SellerEntity(BaseDomainEntity):
    id: Optional[PositiveInt] = None
    seller_id: str
    name: str
    property_id: str
    seller_category_id: int
    seller_config: Optional[dict] = None
    city_id: int
    legal_city_id: Optional[int] = None
    gstin: Optional[str] = None
    legal_name: Optional[str] = None
    legal_address: Optional[str] = None
    pincode: Optional[str] = None
    legal_signature: Optional[str] = None
    legal_pincode: Optional[str] = None
    phone_number: Optional[str] = None
    status: str
    base_currency_code: Optional[str] = None
    timezone: str
    current_business_date: Optional[date] = None
    department_id: Optional[int] = None
    created_from_template_code: Optional[str] = None
    system_interface: Optional[str] = None
    is_auto_created: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None