"""
Base domain entity with common configuration
"""

from pydantic import BaseModel, ConfigDict


class BaseDomainEntity(BaseModel):
    """Base domain entity with common configuration"""

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
        frozen=False,  # Allow mutation for business logic
    )
