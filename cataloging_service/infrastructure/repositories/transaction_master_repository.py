from typing import List, Optional
from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.adapters.transaction_master_adapter import TransactionMasterAdapter
from cataloging_service.domain.entities.transactions.transaction_master import TransactionMasterEntity
from cataloging_service.models import TransactionMaster


@register_instance()
class TransactionMasterRepository(BaseEntityRepository):
    """Repository for transaction master operations"""

    adaptor = TransactionMasterAdapter()

    def create(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        """Create a new transaction master"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_id(self, transaction_id: int) -> Optional[TransactionMasterEntity]:
        """Get transaction master by ID"""
        model = self.rget_by_attr(TransactionMaster, limit_one=True, id=transaction_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_transaction_code(self, transaction_code: str) -> Optional[TransactionMasterEntity]:
        """Get transaction master by transaction code"""
        model = self.rget_by_attr(TransactionMaster, limit_one=True, transaction_code=transaction_code)
        return self.adaptor.to_entity(model) if model else None

    def get_by_property(self, property_id: str, status: Optional[str] = None) -> List[TransactionMasterEntity]:
        """Get all transactions for a property"""
        filters = {"property_id": property_id}
        if status:
            filters["status"] = status
        models = self.rget_by_attr(TransactionMaster, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_transaction_type(self, transaction_type: str, entity_type: str, property_id: Optional[str] = None) -> List[TransactionMasterEntity]:
        """Get transactions by type and entity type"""
        filters = {
            "transaction_type": transaction_type,
            "entity_type": entity_type
        }
        if property_id:
            filters["property_id"] = property_id
        models = self.rget_by_attr(TransactionMaster, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_operational_unit(self, operational_unit_id: str, operational_unit_type: str) -> List[TransactionMasterEntity]:
        """Get transactions by operational unit"""
        models = self.rget_by_attr(
            TransactionMaster,
            operational_unit_id=operational_unit_id,
            operational_unit_type=operational_unit_type
        )
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        """Update transaction master"""
        existing_model = self.rget_by_attr(TransactionMaster, limit_one=True, id=entity.id)
        if not existing_model:
            raise ValueError(f"Transaction master with ID {entity.id} not found")
        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, transaction_id: int) -> bool:
        """Delete transaction master"""
        model = self.rget_by_attr(TransactionMaster, limit_one=True, id=transaction_id)
        if not model:
            return False
        super().delete(model)
        return True

    def exists_by_transaction_code(self, transaction_code: str, exclude_id: Optional[int] = None) -> bool:
        """Check if transaction exists with given transaction code"""
        query = self.session().query(TransactionMaster).filter_by(transaction_code=transaction_code)
        if exclude_id:
            query = query.filter(TransactionMaster.id != exclude_id)
        return query.first() is not None

    def get_department_transactions(self, property_id: str, department_code: str) -> List[TransactionMasterEntity]:
        """Get transactions that have department context"""
        query = self.session().query(TransactionMaster).filter(
            TransactionMaster.property_id == property_id,
            TransactionMaster.transaction_details.contains({"department_code": department_code})
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]
