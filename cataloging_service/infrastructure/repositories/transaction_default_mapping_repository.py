from typing import List, Optional
from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.adapters.transaction_default_mapping_adapter import TransactionDefaultMappingAdapter
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.models import TransactionDefaultMapping


@register_instance()
class TransactionDefaultMappingRepository(BaseEntityRepository):
    """Repository for transaction default mapping operations"""

    adaptor = TransactionDefaultMappingAdapter()

    def create(self, entity: TransactionDefaultMappingEntity) -> TransactionDefaultMappingEntity:
        """Create a new transaction default mapping"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_id(self, mapping_id: int) -> Optional[TransactionDefaultMappingEntity]:
        """Get transaction default mapping by ID"""
        model = self.rget_by_attr(TransactionDefaultMapping, limit_one=True, id=mapping_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_brand(self, brand_id: int, active_only: bool = True) -> List[TransactionDefaultMappingEntity]:
        """Get all transaction default mappings for a brand"""
        filters = {"brand_id": brand_id}
        if active_only:
            filters["is_active"] = True
        models = self.rget_by_attr(TransactionDefaultMapping, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_transaction_type(self, brand_id: int, transaction_type: str, transaction_type_code: str, entity_type: str) -> Optional[TransactionDefaultMappingEntity]:
        """Get transaction default mapping by transaction type criteria"""
        model = self.rget_by_attr(
            TransactionDefaultMapping,
            limit_one=True,
            brand_id=brand_id,
            transaction_type=transaction_type,
            transaction_type_code=transaction_type_code,
            entity_type=entity_type,
            is_active=True
        )
        return self.adaptor.to_entity(model) if model else None

    def get_by_transaction_type_pattern(self, brand_id: int, transaction_type: str, entity_type: str) -> List[TransactionDefaultMappingEntity]:
        """Get all mappings for a transaction type and entity type"""
        models = self.rget_by_attr(
            TransactionDefaultMapping,
            brand_id=brand_id,
            transaction_type=transaction_type,
            entity_type=entity_type,
            is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: TransactionDefaultMappingEntity) -> TransactionDefaultMappingEntity:
        """Update transaction default mapping"""
        existing_model = self.rget_by_attr(TransactionDefaultMapping, limit_one=True, id=entity.id)
        if not existing_model:
            raise ValueError(f"Transaction default mapping with ID {entity.id} not found")
        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, mapping_id: int) -> bool:
        """Delete transaction default mapping"""
        model = self.rget_by_attr(TransactionDefaultMapping, limit_one=True, id=mapping_id)
        if not model:
            return False
        super().delete(model)
        return True

    def exists_by_criteria(self, brand_id: int, transaction_type: str, transaction_type_code: str, entity_type: str, exclude_id: Optional[int] = None) -> bool:
        """Check if mapping exists with given criteria"""
        query = self.session().query(TransactionDefaultMapping).filter_by(
            brand_id=brand_id,
            transaction_type=transaction_type,
            transaction_type_code=transaction_type_code,
            entity_type=entity_type
        )
        if exclude_id:
            query = query.filter(TransactionDefaultMapping.id != exclude_id)
        return query.first() is not None

    def get_all_transaction_types(self, brand_id: int) -> List[str]:
        """Get all unique transaction types for a brand"""
        query = self.session().query(TransactionDefaultMapping.transaction_type).filter_by(
            brand_id=brand_id,
            is_active=True
        ).distinct()
        return [row[0] for row in query.all()]

    def get_all_entity_types(self, brand_id: int, transaction_type: Optional[str] = None) -> List[str]:
        """Get all unique entity types for a brand, optionally filtered by transaction type"""
        query = self.session().query(TransactionDefaultMapping.entity_type).filter_by(
            brand_id=brand_id,
            is_active=True
        )
        if transaction_type:
            query = query.filter_by(transaction_type=transaction_type)
        query = query.distinct()
        return [row[0] for row in query.all()]
