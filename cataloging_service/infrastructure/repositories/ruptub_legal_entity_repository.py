from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import RuptubLegalEntityDetails


class LegalEntityDetailsRepository(BaseRepository):

    def get_all_legal_entities(self):
        return RuptubLegalEntityDetails.query.all()

    def get_legal_entity_details_by_state_id(self, state_id):
        return RuptubLegalEntityDetails.query.filter(RuptubLegalEntityDetails.state_id == state_id).first()
