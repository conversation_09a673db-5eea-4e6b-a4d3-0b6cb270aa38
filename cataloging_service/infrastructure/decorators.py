import functools
import logging

from flask import request
from marshmallow import ValidationError
from treebo_commons.multitenancy.sqlalchemy import db_engine

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.utils import Utils

logger = logging.getLogger(__name__)


def _exception_helper(exception, send_error_mail):
    if send_error_mail:
        Utils.send_email()
    db_engine.get_session().rollback()
    raise exception


def atomic_operation(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            response = func(*args, **kwargs)
            db_engine.get_session().commit()
            return response
        except CatalogingServiceException as exception:
            if exception.force_commit:
                db_engine.get_session().commit()
            raise  # Let global error handler deal with it
        except Exception:
            raise  # Let global error handler deal with it

    return wrapper


def validator(schema, request_attr):
    """
        @request_attr is 'args' for GET APIs
                         'form' for POST APIs
                         'json' for POST APIs content-type application/json
    """

    def validate_decorator(f):
        def wrapper(*args, **kwargs):
            request_data = getattr(request, request_attr)

            try:
                parsed_request = schema().load(request_data)
            except ValidationError as e:
                logger.info("Errors from %s schema: %s", schema, e.messages)
                raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context=e.messages)


            kwargs['parsed_request'] = parsed_request
            return f(*args, **kwargs)

        return functools.update_wrapper(wrapper, f)

    return validate_decorator


def query_params(schema):
    return validator(schema, 'args')


def form_data(schema):
    return validator(schema, 'form')


def raw_json(schema):
    return validator(schema, 'json')


def log_request(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        from timeit import default_timer as timer
        start_time = timer()
        logger.info('Url: %s', request.url)
        logger.info('Headers: %s', request.headers)
        logger.info('Body: %s', request.get_data())
        result = func(*args, **kwargs)
        end_time = timer()
        total_response_time = end_time - start_time
        logger.info("PERF: Function: {func_name} => Total Response time: {time} seconds".format(
            func_name=func.__name__,
            time=total_response_time),
        )
        return result
    return wrapper
