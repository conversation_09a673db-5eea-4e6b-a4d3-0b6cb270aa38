from datetime import datetime
from typing import Optional
from cataloging_service.domain.entities.templates.department_template import DepartmentTemplateEntity
from cataloging_service.models import DepartmentTemplate


class DepartmentTemplateAdapter:
    """Adapter for department template entity and model conversion"""

    @staticmethod
    def to_entity(model: DepartmentTemplate) -> DepartmentTemplateEntity:
        """Convert database model to domain entity"""
        return DepartmentTemplateEntity(
            id=model.id,
            brand_id=model.brand_id,
            code=model.code,
            name=model.name,
            parent_code=model.parent_code,
            description=model.description,
            financial_code=model.financial_code,
            is_active=model.is_active,
            auto_create_on_property_launch=model.auto_create_on_property_launch,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: DepartmentTemplateEntity) -> DepartmentTemplate:
        """Convert domain entity to database model"""
        model = DepartmentTemplate()
        if entity.id:
            model.id = entity.id
        model.brand_id = entity.brand_id
        model.code = entity.code
        model.name = entity.name
        model.parent_code = entity.parent_code
        model.description = entity.description
        model.financial_code = entity.financial_code
        model.is_active = entity.is_active
        model.auto_create_on_property_launch = entity.auto_create_on_property_launch
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        return model

    @staticmethod
    def update_model_from_entity(model: DepartmentTemplate, entity: DepartmentTemplateEntity) -> DepartmentTemplate:
        """Update existing model with entity data"""
        model.brand_id = entity.brand_id
        model.code = entity.code
        model.name = entity.name
        model.parent_code = entity.parent_code
        model.description = entity.description
        model.financial_code = entity.financial_code
        model.is_active = entity.is_active
        model.auto_create_on_property_launch = entity.auto_create_on_property_launch
        model.modified_at = datetime.utcnow()
        return model
