from datetime import datetime
from typing import Optional
from cataloging_service.domain.entities.properties.property_department import PropertyDepartmentEntity
from cataloging_service.models import Department


class PropertyDepartmentAdapter:
    """Adapter for property department entity and model conversion"""

    @staticmethod
    def to_entity(model: Department) -> PropertyDepartmentEntity:
        """Convert database model to domain entity"""
        return PropertyDepartmentEntity(
            id=model.id,
            property_id=model.property_id,
            template_code=model.template_code,
            code=model.code,
            name=model.name,
            parent_id=model.parent_id,
            financial_code=model.financial_code,
            description=model.description,
            is_custom=model.is_custom,
            is_active=model.is_active,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: PropertyDepartmentEntity) -> Department:
        """Convert domain entity to database model"""
        model = Department()
        if entity.id:
            model.id = entity.id
        model.property_id = entity.property_id
        model.template_code = entity.template_code
        model.code = entity.code
        model.name = entity.name
        model.parent_id = entity.parent_id
        model.financial_code = entity.financial_code
        model.description = entity.description
        model.is_custom = entity.is_custom
        model.is_active = entity.is_active
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        return model

    @staticmethod
    def update_model_from_entity(model: Department, entity: PropertyDepartmentEntity) -> Department:
        """Update existing model with entity data"""
        model.property_id = entity.property_id
        model.template_code = entity.template_code
        model.code = entity.code
        model.name = entity.name
        model.parent_id = entity.parent_id
        model.financial_code = entity.financial_code
        model.description = entity.description
        model.is_custom = entity.is_custom
        model.is_active = entity.is_active
        model.modified_at = datetime.utcnow()
        return model
