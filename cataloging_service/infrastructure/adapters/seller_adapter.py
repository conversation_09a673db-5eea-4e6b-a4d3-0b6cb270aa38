from cataloging_service.domain.entities.seller.seller import SellerEntity
from cataloging_service.models import Seller

class SellerAdapter:
    """Adapter for Seller entity and model conversion"""

    @staticmethod
    def to_entity(model: Seller) -> SellerEntity:
        """Convert database model to domain entity"""
        return SellerEntity(
            id=model.id,
            seller_id=model.seller_id,
            name=model.name,
            property_id=model.property_id,
            seller_category_id=model.seller_category_id,
            seller_config=model.seller_config,
            city_id=model.city_id,
            legal_city_id=model.legal_city_id,
            gstin=model.gstin,
            legal_name=model.legal_name,
            legal_address=model.legal_address,
            pincode=model.pincode,
            legal_signature=model.legal_signature,
            legal_pincode=model.legal_pincode,
            phone_number=model.phone_number,
            status=model.status,
            base_currency_code=model.base_currency_code,
            timezone=model.timezone,
            current_business_date=model.current_business_date,
            department_id=model.department_id,
            created_from_template_code=model.created_from_template_code,
            system_interface=model.system_interface,
            is_auto_created=model.is_auto_created,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: SellerEntity) -> Seller:
        """Convert domain entity to database model"""
        model = Seller()
        if entity.id:
            model.id = entity.id
        model.seller_id = entity.seller_id
        model.name = entity.name
        model.property_id = entity.property_id
        model.seller_category_id = entity.seller_category_id
        model.seller_config = entity.seller_config
        model.city_id = entity.city_id
        model.legal_city_id = entity.legal_city_id
        model.gstin = entity.gstin
        model.legal_name = entity.legal_name
        model.legal_address = entity.legal_address
        model.pincode = entity.pincode
        model.legal_signature = entity.legal_signature
        model.legal_pincode = entity.legal_pincode
        model.phone_number = entity.phone_number
        model.status = entity.status
        model.base_currency_code = entity.base_currency_code
        model.timezone = entity.timezone
        model.current_business_date = entity.current_business_date
        model.department_id = entity.department_id
        model.created_from_template_code = entity.created_from_template_code
        model.system_interface = entity.system_interface
        model.is_auto_created = entity.is_auto_created
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        return model