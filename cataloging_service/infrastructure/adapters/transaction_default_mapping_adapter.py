from datetime import datetime
from typing import Optional
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.models import TransactionDefaultMapping


class TransactionDefaultMappingAdapter:
    """Adapter for transaction default mapping entity and model conversion"""

    @staticmethod
    def to_entity(model: TransactionDefaultMapping) -> TransactionDefaultMappingEntity:
        """Convert database model to domain entity"""
        return TransactionDefaultMappingEntity(
            id=model.id,
            brand_id=model.brand_id,
            transaction_type=model.transaction_type,
            transaction_type_code=model.transaction_type_code,
            entity_type=model.entity_type,
            default_gl_code=model.default_gl_code,
            default_erp_id=model.default_erp_id,
            default_particulars=model.default_particulars,
            default_is_merge=model.default_is_merge,
            transaction_details=model.transaction_details or {},
            is_active=model.is_active,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: TransactionDefaultMappingEntity) -> TransactionDefaultMapping:
        """Convert domain entity to database model"""
        model = TransactionDefaultMapping()
        if entity.id:
            model.id = entity.id
        model.brand_id = entity.brand_id
        model.transaction_type = entity.transaction_type
        model.transaction_type_code = entity.transaction_type_code
        model.entity_type = entity.entity_type
        model.default_gl_code = entity.default_gl_code
        model.default_erp_id = entity.default_erp_id
        model.default_particulars = entity.default_particulars
        model.default_is_merge = entity.default_is_merge
        model.transaction_details = entity.transaction_details
        model.is_active = entity.is_active
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        return model

    @staticmethod
    def update_model_from_entity(model: TransactionDefaultMapping, entity: TransactionDefaultMappingEntity) -> TransactionDefaultMapping:
        """Update existing model with entity data"""
        model.brand_id = entity.brand_id
        model.transaction_type = entity.transaction_type
        model.transaction_type_code = entity.transaction_type_code
        model.entity_type = entity.entity_type
        model.default_gl_code = entity.default_gl_code
        model.default_erp_id = entity.default_erp_id
        model.default_particulars = entity.default_particulars
        model.default_is_merge = entity.default_is_merge
        model.transaction_details = entity.transaction_details
        model.is_active = entity.is_active
        model.modified_at = datetime.utcnow()
        return model
