{% extends 'admin/model/edit.html' %}

{% from 'admin/lib.html' import extra with context %}

{% from 'admin/lib.html' import form_tag with context %}

{% from 'admin/lib.html' import render_form_fields with context %}

{% macro my_render_form_buttons(cancel_url, extra=None, is_modal=False) %}
    <script>
        function confirm_live() {
            status_field = document.getElementById('status')
            if (status_field.value == 'LIVE') {
                return confirm('You sure?')
            }

            return true
        }
        document.getElementsByTagName('form').onsubmit = confirm_live
    </script>

    <hr>

    <div class="form-group">

        <div class="col-md-offset-2 col-md-10 submit-row">

            <input type="submit" class="btn btn-primary" value="{{ _gettext('Submit') }}"/>

            {% if extra %}

                {{ extra }}

            {% endif %}

            {% if cancel_url %}

                <a href="{{ cancel_url }}" class="btn btn-danger" role="button"
                   {% if is_modal %}data-dismiss="modal"{% endif %}>{{ _gettext('Cancel') }}</a>

            {% endif %}

        </div>

    </div>

{% endmacro %}

{% macro my_form_tag(form=None, action=None) %}
    <script>
        function go_live() {
            status = document.getElementById('status').value
            if (status == 'LIVE') {
                return confirm('You are saving this property in Live state. Make sure that all required information is provided before you continue. Proceed?')
            }
            return True
        }
    </script>
    <form action="{{ action or '' }}" method="POST" role="form" class="admin-form form-horizontal"
          enctype="multipart/form-data" onsubmit="return go_live()">
        {{ caller() }}
    </form>
{% endmacro %}

{% macro my_render_form(form, cancel_url, extra=None, form_opts=None, action=None, is_modal=False) -%}

    {% call my_form_tag(action=action) %}

        {{ render_form_fields(form, form_opts=form_opts) }}

        {{ my_render_form_buttons(cancel_url, extra, is_modal) }}

    {% endcall %}

{% endmacro %}

{% block edit_form %}

    {{ my_render_form(form, return_url, extra(), form_opts) }}

{% endblock %}
