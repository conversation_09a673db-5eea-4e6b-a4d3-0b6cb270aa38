import logging
import traceback

from cataloging_service.api.v3.schemas.user_role_creation import UserRoleCreationSchema
from cataloging_service.client.authn_client import AuthNClient
from cataloging_service.client.authz_client import AuthZClient

logger = logging.getLogger(__name__)


def user_role_creation(data):
    try:
        for user_role_detail in data['user_role_details']:
            pin = user_role_detail['first_name'].lower() + '@15672'
            auth_id = AuthNClient.create_user(user_role_detail['first_name'], user_role_detail['last_name'],
                                                     user_role_detail['email'], pin=pin)
            AuthZClient.assign_role(auth_id, user_role_detail['hotel_id'] if user_role_detail['hotel_id'] else None,
                                    user_role_detail['role'], user_role_detail['application_id'])
        return

    except Exception as e:
        logger.exception(e)
        traceback.print_exc()
        raise
