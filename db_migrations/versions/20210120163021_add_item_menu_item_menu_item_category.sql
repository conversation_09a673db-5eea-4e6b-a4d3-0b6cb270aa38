-- revision: '20210120163021_add_item_menu_item_menu_item_category'
-- down_revision: '20210120154147_add_frequency_and_offering_in_sku'

-- upgrade
--
-- Name: item, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE item (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    name character varying NOT NULL,
    code character varying,
    description character varying,
    sku_category_code character varying,
    display_name character varying,
    print_name character varying,
    prep_time interval NOT NULL,
    use_as_side boolean NOT NULL,
    contains_alcohol boolean NOT NULL,
    pre_tax_price decimal,
    cost decimal,
    seller_id character varying NOT NULL,
    sku_id int
);

ALTER TABLE ONLY item
    ADD CONSTRAINT item_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id) ON DELETE CASCADE;

--
-- Name: item_variant, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE item_variant (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    name character varying NOT NULL,
    display_order int NOT NULL,
    pre_tax_price decimal NOT NULL,
    cost decimal NOT NULL,
    sku_id int NOT NULL,
    item_id int NOT NULL,
    sku_category_code character varying NOT NULL
);

ALTER TABLE ONLY item_variant
    ADD CONSTRAINT item_variant_item_id_fkey FOREIGN KEY (item_id) REFERENCES item(id) ON DELETE CASCADE;

--
-- Name: menu_item, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE menu_item (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    menu_id int NOT NULL,
    item_id int NOT NULL,
    item_variant_id int,
    sold_out boolean NOT NULL,
    display_order int NOT NULL
);

ALTER TABLE ONLY menu_item
    ADD CONSTRAINT menu_item_menu_id_fkey FOREIGN KEY (menu_id) REFERENCES menu(id) ON DELETE CASCADE;

ALTER TABLE ONLY menu_item
    ADD CONSTRAINT menu_item_item_id_fkey FOREIGN KEY (item_id) REFERENCES item(id) ON DELETE CASCADE;

ALTER TABLE ONLY menu_item
    ADD CONSTRAINT menu_item_item_variant_id FOREIGN KEY (item_variant_id) REFERENCES item_variant(id) ON DELETE CASCADE;

--
-- Name: menu_item_category, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE menu_item_category(
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    menu_item_id int NOT NULL,
    menu_category_id int NOT NULL
);

ALTER TABLE ONLY menu_item_category
    ADD CONSTRAINT menu_item_category_menu_category_id_fkey FOREIGN KEY (menu_category_id) REFERENCES menu_category(id) ON DELETE CASCADE;

ALTER TABLE ONLY menu_item_category
    ADD CONSTRAINT menu_item_category_menu_item_id_fkey FOREIGN KEY (menu_item_id) REFERENCES menu_item(id) ON DELETE CASCADE;


-- downgrade

DROP TABLE menu_item_category;
DROP TABLE menu_item;
DROP TABLE item_variant;
DROP TABLE item;
