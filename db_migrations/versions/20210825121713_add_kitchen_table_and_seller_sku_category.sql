-- revision: '20210825121713_add_kitchen_table_and_seller_sku_category'
-- down_revision: '20210901103815_sku_new_field_tax_at_room_rate'

-- upgrade

CREATE table kitchen (
  created_at timestamptz,
  modified_at timestamptz,
  is_deleted boolean NOT NULL DEFAULT FALSE,
  id serial NOT NULL,
  property_id character varying NOT NULL,
  name character varying NOT NULL,
  config json,
  CONSTRAINT kitchen_pkey PRIMARY KEY (id)
);

ALTER table item add column kitchen_id integer;
ALTER TABLE ONLY item
    ADD CONSTRAINT kitchen_id_fkey FOREIGN KEY (kitchen_id) REFERENCES kitchen(id);


CREATE TABLE seller_sku_category (
    seller_id character varying NOT NULL,
    sku_category_id integer NOT NULL,
    created_at timestamptz,
    modified_at timestamptz
);

ALTER TABLE ONLY seller_sku_category
    ADD CONSTRAINT seller_sku_category_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id) ON DELETE CASCADE;

ALTER TABLE ONLY seller_sku_category
    ADD CONSTRAINT seller_sku_category_sku_category_id_fkey FOREIGN KEY (sku_category_id) REFERENCES sku_category(id) ON DELETE CASCADE;


-- downgrade
ALTER TABLE item drop column kitchen_id;
DROP TABLE kitchen;
DROP TABLE seller_sku_category;
