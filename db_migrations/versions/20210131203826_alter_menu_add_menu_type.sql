-- revision: '20210131203826_alter_menu_add_menu_type'
-- down_revision: '20210120163021_add_item_menu_item_menu_item_category'

-- upgrade
--
-- Name: menu_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE menu_type AS ENUM (
    'RESTAURANT',
    'TAKE_AWAY',
    'DELIVERY',
    'ROOM_SERVICE'
);

ALTER TABLE menu ADD COLUMN menu_type menu_type not null;

ALTER TABLE menu ALTER COLUMN display_name DROP NOT NULL;
ALTER TABLE menu ALTER COLUMN description DROP NOT NULL;

ALTER TABLE menu ADD is_deleted boolean NOT NULL DEFAULT FALSE; 
ALTER TABLE menu_timing ADD is_deleted boolean NOT NULL DEFAULT FALSE; 
ALTER TABLE menu_category ADD is_deleted boolean NOT NULL DEFAULT FALSE; 
ALTER TABLE menu_category DROP CONSTRAINT menu_category_menu_id_name_key;

-- downgrade

DROP TYPE menu_type CASCADE;

