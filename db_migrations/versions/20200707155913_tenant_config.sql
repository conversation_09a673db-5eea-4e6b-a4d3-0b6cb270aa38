-- revision: '20200707155913_tenant_config'
-- down_revision: '20200702223638_hygiene_shield'

-- upgrade
CREATE TABLE available_config (
    name character varying NOT NULL,
    value_type character varying NOT NULL
);

ALTER TABLE ONLY available_config
    ADD CONSTRAINT available_config_pkey PRIMARY KEY (name);

CREATE TABLE tenant_config (
    config_name character varying NOT NULL,
    config_value character varying NOT NULL
);

ALTER TABLE ONLY tenant_config
    ADD CONSTRAINT tenant_config_pkey PRIMARY KEY (config_name);

ALTER TABLE ONLY tenant_config
    ADD CONSTRAINT tenant_config_config_name_fkey FOREIGN KEY (config_name) REFERENCES available_config(name);

-- downgrade
DROP CONSTRAINT tenant_config_config_name_fkey;
DROP TABLE tenant_config;
DROP TABLE available_config;
