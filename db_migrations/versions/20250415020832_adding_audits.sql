-- revision: '20250415020832_adding_audits'
-- down_revision: '20250401171143_add_brand_code_in_brand_table_and_remove_brand_from_property'


-- upgrade
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    user_action VARCHAR,
    user_details JSON<PERSON>,
    entity_name VA<PERSON>HA<PERSON>,
    entity_id VARCHAR,
    request_id VARCHAR,
    source VARCHAR,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    details JSONB
);

CREATE INDEX idx_audit_logs_user_action ON audit_logs(user_action);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_name, entity_id);
-- downgrade
DROP INDEX IF EXISTS idx_audit_logs_entity;
DROP INDEX IF EXISTS idx_audit_logs_timestamp;
DROP INDEX IF EXISTS idx_audit_logs_user_action;
DROP TABLE audit_logs;
