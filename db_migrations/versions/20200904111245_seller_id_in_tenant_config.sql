-- revision: '20200904111245_seller_id_in_tenant_config'
-- down_revision: '20200930105413_required_fields_bankdetails'

-- upgrade
ALTER TABLE tenant_config DROP CONSTRAINT tenant_config_config_name_property_id_key;
ALTER TABLE tenant_config DROP CONSTRAINT _unique_config_name_property_id;
DROP index _unique_config_name_with_property_null;

ALTER TABLE tenant_config ADD COLUMN seller_id character varying;
ALTER TABLE tenant_config ADD UNIQUE(config_name, property_id, seller_id);
CREATE UNIQUE INDEX _unique_config_name_with_property_null_seller_null ON tenant_config (config_name) WHERE property_id is NULL and seller_id IS NULL;
CREATE UNIQUE INDEX _unique_config_name_with_property_not_null_seller_null ON tenant_config (config_name, property_id) WHERE property_id is not NULL and seller_id IS NULL;
CREATE UNIQUE INDEX _unique_config_name_with_seller_not_null_and_property_null ON tenant_config (config_name, seller_id) WHERE property_id is NULL and seller_id IS not NULL;
ALTER TABLE tenant_config
    ADD CONSTRAINT tenant_config_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id);

-- downgrade
ALTER TABLE tenant_config DROP COLUMN seller_id;

ALTER TABLE ONLY tenant_config
    ADD CONSTRAINT _unique_config_name_property_id UNIQUE (config_name, property_id);
ALTER TABLE tenant_config ADD UNIQUE(config_name, property_id);
CREATE UNIQUE INDEX _unique_config_name_with_property_null ON tenant_config (config_name, (property_id IS NULL)) WHERE property_id IS NULL;